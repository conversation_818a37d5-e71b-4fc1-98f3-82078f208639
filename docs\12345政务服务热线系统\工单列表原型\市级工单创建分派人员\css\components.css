/**
 * 组件样式文件
 * 包含工具栏、表格、筛选器等组件样式
 */

/* 现代化工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px 24px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 24px;
    gap: 20px;
    border: 1px solid var(--gray-200);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 现代化搜索容器 */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 350px;
    max-width: 500px;
}

.search-input {
    flex: 1;
    padding: 12px 48px 12px 16px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-300);
    background: var(--gray-50);
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: #fff;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-input::placeholder {
    color: var(--gray-500);
}

.search-btn {
    position: absolute;
    right: 8px;
    border: none;
    background: var(--primary-color);
    color: #fff;
    padding: 8px 12px;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

/* 批量操作区域 */
.batch-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: #e8f4fd;
    border-radius: 6px;
    border: 1px solid #3498db;
}

.selected-count {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

/* 现代化筛选标签页 */
.filter-tabs {
    display: flex;
    background: #fff;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 24px;
    overflow-x: auto;
    border: 1px solid var(--gray-200);
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.filter-tabs::-webkit-scrollbar {
    display: none;
}

.tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    border-bottom: 3px solid transparent;
    min-width: 140px;
    justify-content: center;
    background: transparent;
    position: relative;
    font-weight: 500;
}

.tab-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-md);
}

.tab-item:hover {
    background-color: var(--gray-50);
    transform: translateY(-1px);
}

.tab-item.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: #fff;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.tab-text {
    font-size: 14px;
    font-weight: 600;
    z-index: 1;
    position: relative;
}

.tab-badge {
    background-color: var(--gray-400);
    color: #fff;
    padding: 4px 8px;
    border-radius: var(--radius-xl);
    font-size: 12px;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
    z-index: 1;
    position: relative;
    transition: all 0.3s ease;
}

.tab-badge.urgent {
    background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
    animation: pulse 2s infinite;
}

.tab-item.active .tab-badge {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 现代化高级搜索面板 */
.advanced-search-panel {
    background: #fff;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 24px;
    overflow: hidden;
    border: 1px solid var(--gray-200);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-form {
    padding: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 4px;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 12px;
}

.date-range input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--gray-50);
    transition: all 0.3s ease;
}

.date-range input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: #fff;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.date-range span {
    font-size: 14px;
    color: var(--gray-500);
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 16px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-200);
    justify-content: flex-end;
}

/* 现代化表格容器 */
.table-container {
    background: #fff;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow-x: auto;
    overflow-y: hidden;
    margin-bottom: 24px;
    width: 100%;
    border: 1px solid var(--gray-200);
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

/* 现代化表格样式 */
.ticket-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 14px;
    background: #fff;
}

.ticket-table th,
.ticket-table td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
    white-space: nowrap;
    transition: all 0.3s ease;
}

.ticket-table th {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    font-weight: 700;
    color: var(--gray-700);
    position: sticky;
    top: 0;
    z-index: 10;
    border-top: none;
    border-bottom: 2px solid var(--gray-300);
}

.ticket-table tbody tr {
    transition: all 0.3s ease;
    border-radius: var(--radius-md);
}

.ticket-table tbody tr:hover {
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(37, 99, 235, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.ticket-table tbody tr.selected {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    border-left: 4px solid var(--primary-color);
}

/* 列宽设置 */
.checkbox-col {
    width: 48px;
    text-align: center;
}

.actions-col {
    width: 140px;
    text-align: center;
}

.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all 0.3s ease;
}

.sortable:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.sortable i {
    margin-left: 6px;
    font-size: 12px;
    color: var(--gray-400);
    transition: all 0.3s ease;
}

.sortable.sorted i {
    color: var(--primary-color);
    transform: rotate(180deg);
}

.sortable.sorted.desc i {
    transform: rotate(0deg);
}

/* 现代化状态标签 */
.status-badge {
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid transparent;
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.status-draft {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--gray-600);
    border-color: var(--gray-300);
}

.status-pending {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #f59e0b;
}

.status-processing {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-color: #3b82f6;
}

.status-closed {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border-color: #10b981;
}

/* 现代化优先级标签 */
.priority-badge {
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    min-width: 70px;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid transparent;
}

.priority-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.priority-urgent {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-color: var(--error-color);
    animation: pulse 2s infinite;
}

.priority-high {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: var(--warning-color);
}

.priority-normal {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-color: var(--info-color);
}

.priority-low {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border-color: var(--success-color);
}

/* 现代化剩余时间 */
.time-remaining {
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.time-remaining::before {
    content: '⏰';
    font-size: 14px;
}

.time-remaining.urgent {
    color: var(--error-color);
    font-weight: 700;
}

.time-remaining.warning {
    color: var(--warning-color);
}

.time-remaining.normal {
    color: var(--success-color);
}

/* 现代化操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 4px;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.view {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: #fff;
}

.action-btn.edit {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: #fff;
}

.action-btn.assign {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: #fff;
}

.action-btn.close {
    background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%);
    color: #fff;
}

/* 现代化分页容器 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px 24px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: var(--gray-600);
    font-weight: 500;
}

.pagination-info select {
    width: auto;
    min-width: 70px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--gray-50);
    transition: all 0.3s ease;
    font-size: 14px;
}

.pagination-info select:focus {
    outline: none;
    border-color: var(--primary-color);
    background: #fff;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 现代化分页按钮 */
.pagination {
    display: flex;
    gap: 8px;
}

.page-btn {
    padding: 8px 16px;
    border: 1px solid var(--gray-300);
    background: #fff;
    color: var(--gray-600);
    cursor: pointer;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.page-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.page-btn:hover:not(:disabled)::before {
    left: 100%;
}

.page-btn:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.page-btn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-color: var(--primary-color);
    color: #fff;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.6;
}

/* 下拉菜单 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 120px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 8px 12px;
    color: #2c3e50;
    text-decoration: none;
    font-size: 13px;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #3498db;
}

.dropdown-item.danger:hover {
    background-color: #f8d7da;
    color: #721c24;
}

/* 模态框基础样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #7f8c8d;
    cursor: pointer;
    padding: 4px;
}

.modal-close:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* ==================== 快速预览面板样式 ==================== */

/* 预览面板容器 */
.preview-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    justify-content: flex-end;
}

/* 预览面板遮罩层 */
.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    transition: opacity 0.3s ease;
}

/* 预览面板内容区 */
.preview-content {
    width: 600px;
    height: 100%;
    background: #fff;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1001;
}

/* 面板显示状态 */
.preview-panel.show .preview-content {
    transform: translateX(0);
}

/* 预览面板头部 */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    gap: 16px;
}

.preview-title-section {
    flex: 1;
    min-width: 0;
}

.preview-ticket-id {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-600);
    margin-bottom: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
}

.preview-ticket-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.4;
    margin-bottom: 12px;
    word-wrap: break-word;
}

.preview-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.preview-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.preview-tag.urgent {
    background: var(--red-100);
    color: var(--red-700);
    border: 1px solid var(--red-200);
}

.preview-tag.important {
    background: var(--orange-100);
    color: var(--orange-700);
    border: 1px solid var(--orange-200);
}

.preview-tag.returned {
    background: var(--yellow-100);
    color: var(--yellow-700);
    border: 1px solid var(--yellow-200);
}

.preview-tag.supervised {
    background: var(--purple-100);
    color: var(--purple-700);
    border: 1px solid var(--purple-200);
}

.preview-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--radius-full);
    font-size: 12px;
    font-weight: 500;
    gap: 4px;
}

.preview-tag.urgent {
    background: var(--red-100);
    color: var(--red-700);
    border: 1px solid var(--red-200);
}

.preview-tag.important {
    background: var(--orange-100);
    color: var(--orange-700);
    border: 1px solid var(--orange-200);
}

.preview-tag.returned {
    background: var(--yellow-100);
    color: var(--yellow-700);
    border: 1px solid var(--yellow-200);
}

.preview-tag.supervised {
    background: var(--purple-100);
    color: var(--purple-700);
    border: 1px solid var(--purple-200);
}

.preview-close-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.preview-close-btn:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

/* 核心操作区 */
.preview-actions {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    background: #fff;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.preview-actions .btn {
    font-size: 14px;
    padding: 10px 16px;
    border-radius: var(--radius-md);
}

.preview-actions .btn-primary {
    background: var(--primary-600);
    color: white;
    border: 1px solid var(--primary-600);
}

.preview-actions .btn-outline {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.preview-actions .btn-danger {
    background: var(--red-600);
    color: white;
    border: 1px solid var(--red-600);
}

/* 预览面板主体内容 */
.preview-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* 预览面板章节 */
.preview-section {
    border-bottom: 1px solid var(--gray-200);
}

.preview-section:last-child {
    border-bottom: none;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    padding: 20px 24px 16px;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.section-content {
    padding: 20px 24px;
}

/* 信息项样式 */
.info-item {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item label {
    font-weight: 600;
    color: var(--gray-700);
    min-width: 100px;
    flex-shrink: 0;
    font-size: 14px;
}

.info-item span,
.info-item .content-text {
    color: var(--gray-900);
    flex: 1;
    line-height: 1.5;
    font-size: 14px;
}

.content-text {
    background: var(--gray-50);
    padding: 12px;
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
}

/* 市民信息行 */
.citizen-info-row {
    display: flex;
    gap: 32px;
    margin-bottom: 16px;
}

.citizen-info-row .info-item {
    margin-bottom: 0;
    flex: 1;
}

/* 分类信息行 */
.classification-row {
    display: flex;
    gap: 32px;
    margin-bottom: 16px;
}

.classification-row .info-item {
    margin-bottom: 0;
    flex: 1;
}

/* 复制按钮和链接按钮 */
.btn-copy,
.btn-link {
    background: none;
    border: none;
    color: var(--primary-600);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.btn-copy:hover,
.btn-link:hover {
    background: var(--primary-50);
    color: var(--primary-700);
}

/* 附件列表 */
.attachments-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 13px;
    color: var(--gray-700);
    cursor: pointer;
    transition: all 0.2s ease;
}

.attachment-item:hover {
    background: var(--gray-100);
    border-color: var(--gray-300);
}

.attachment-item i {
    color: var(--primary-600);
}

/* 地图容器 */
.map-container {
    width: 100%;
    height: 200px;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--gray-50);
}

.map-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s ease;
}

.map-placeholder:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.map-placeholder i {
    font-size: 24px;
    margin-bottom: 8px;
}

/* 退回原因样式 */
.return-reason {
    background: var(--red-50);
    border: 1px solid var(--red-200);
    border-radius: var(--radius-md);
    padding: 16px;
    margin-bottom: 20px;
}

.return-reason label {
    color: var(--red-700);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.return-content {
    color: var(--red-800);
    background: white;
    padding: 12px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--red-200);
    line-height: 1.5;
}

/* 流转历史 */
.flow-history {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: 16px;
}

.flow-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid var(--gray-200);
    font-size: 13px;
}

.flow-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.flow-time {
    color: var(--gray-600);
    font-weight: 500;
    min-width: 60px;
    flex-shrink: 0;
}

.flow-content {
    flex: 1;
    color: var(--gray-800);
    line-height: 1.4;
}

.flow-action {
    font-weight: 600;
    color: var(--primary-600);
}

.flow-unit {
    color: var(--gray-700);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .preview-content {
        width: 100%;
    }

    .citizen-info-row,
    .classification-row {
        flex-direction: column;
        gap: 16px;
    }

    .preview-header {
        padding: 16px;
    }

    .section-content {
        padding: 16px;
    }

    .preview-actions {
        padding: 16px;
    }

    .info-item {
        flex-direction: column;
        gap: 4px;
    }

    .info-item label {
        min-width: auto;
    }
}
