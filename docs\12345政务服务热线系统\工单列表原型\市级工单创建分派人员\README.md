# 市级工单创建分派人员 - 工单列表原型

## 项目概述

这是一个专为市级工单创建分派人员设计的工单列表管理系统原型，基于《14.2、市级工单创建分派人员.md》文档的需求设计实现。

## 功能特性

### 🎯 核心功能

- **工单列表展示**：支持多字段展示，包括工单编号、诉求标题、状态、优先级等
- **快捷筛选**：提供7个预设筛选标签，快速切换不同工作视图
- **高级搜索**：支持多条件组合查询，精确定位目标工单
- **批量操作**：支持批量指派、批量办结、批量导出等操作
- **行内操作**：提供查看、编辑、指派、办结等单条工单操作

### 📊 数据展示

#### 列表核心字段
- 工单编号 (wo_id)
- 诉求标题 (title)
- 当前状态 (current_status)
- 紧急程度 (priority)
- 诉求渠道 (channel)
- 工单类型 (wo_type)
- 市民姓名 (citizen_name)
- 创建人姓名 (creator_name)
- 创建时间 (create_time)
- 责任单位 (responsible_unit)
- 总体处理剩余时间 (remaining_time_total)

#### 快捷筛选标签
- **我的草稿**：显示当前用户创建的草稿状态工单
- **被退回件**：显示被下级单位退回的工单
- **已派待接**：显示已派发但未被接收的工单
- **我已办结**：显示当前用户即时办结的工单
- **今日新增**：显示今天创建的所有工单
- **紧急/重大**：显示紧急和重大优先级的工单
- **全部工单**：显示所有权限范围内的工单

### 🔧 操作功能

#### 页面级操作
- 新建工单
- 刷新列表
- 搜索工单
- 高级搜索
- 批量指派
- 批量办结
- 导出工单

#### 行内操作
- 查看详情
- 编辑工单（仅草稿状态）
- 指派工单（仅草稿状态）
- 即时办结（仅草稿状态）
- 催办工单（已派发状态）
- 撤回工单（已派发但未接收）
- 复制工单
- 添加标签

## 技术架构

### 前端技术栈
- **HTML5**：语义化标记和现代化结构
- **CSS3**：模块化样式设计，支持响应式布局
- **JavaScript (ES6+)**：原生JavaScript，无框架依赖
- **Font Awesome**：图标库

### 文件结构
```
工单列表原型（新）/
├── index.html                 # 主页面文件
├── css/                       # 样式文件目录
│   ├── base.css              # 基础样式和重置
│   ├── components.css        # 组件样式
│   └── responsive.css        # 响应式样式
├── js/                       # JavaScript文件目录
│   ├── config.js             # 配置和常量
│   ├── mock-data.js          # 模拟数据
│   ├── controller.js         # 主控制器
│   └── main.js               # 应用入口
└── README.md                 # 项目说明文档
```

### 架构设计

#### 模块化设计
- **配置模块** (config.js)：系统配置、枚举值、权限配置
- **数据模块** (mock-data.js)：模拟数据和API响应
- **控制器模块** (controller.js)：业务逻辑和数据管理
- **主应用模块** (main.js)：应用初始化和全局事件

#### 样式架构
- **基础样式** (base.css)：重置样式、字体、基础组件
- **组件样式** (components.css)：表格、按钮、表单等组件
- **响应式样式** (responsive.css)：适配不同屏幕尺寸

## 使用指南

### 快速开始

1. **打开页面**
   ```bash
   # 直接在浏览器中打开 index.html
   open index.html
   ```

2. **功能演示**
   - 页面加载后会自动显示模拟数据
   - 默认显示"我的草稿"筛选视图
   - 可以尝试各种筛选、搜索、排序功能

### 主要操作流程

#### 筛选工单
1. 点击顶部筛选标签切换不同视图
2. 使用搜索框进行关键词搜索
3. 点击"高级搜索"进行多条件查询

#### 批量操作
1. 勾选需要操作的工单
2. 在顶部批量操作区域选择操作类型
3. 确认执行批量操作

#### 单条操作
1. 在工单行右侧点击对应操作按钮
2. 根据工单状态显示不同的可用操作
3. 确认执行操作

### 权限说明

系统根据用户角色和工单状态控制操作权限：

- **编辑权限**：仅限草稿状态且为当前用户创建的工单
- **指派权限**：仅限草稿状态且为当前用户创建的工单
- **办结权限**：仅限草稿状态且为当前用户创建的工单
- **撤回权限**：仅限已派发但未接收且为当前用户创建的工单
- **催办权限**：仅限已派发状态的工单

## 数据说明

### 工单状态
- **草稿** (draft)：新创建或被退回的工单
- **待接收** (pending)：已派发但未被承办单位接收
- **处理中** (processing)：承办单位正在处理
- **已关闭** (closed)：已完成处理或即时办结

### 优先级
- **紧急** (urgent)：需要立即处理的工单
- **重要** (high)：优先级较高的工单
- **一般** (normal)：常规优先级工单
- **较低** (low)：优先级较低的工单

### 工单类型
- **投诉** (complaint)：市民投诉类工单
- **咨询** (consultation)：政策咨询类工单
- **建议** (suggestion)：意见建议类工单
- **求助** (help)：紧急求助类工单

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 性能特性

- **响应式设计**：适配桌面、平板、手机等设备
- **模块化加载**：CSS和JS文件按功能模块组织
- **防抖搜索**：搜索输入防抖处理，提升性能
- **虚拟滚动**：大数据量时的性能优化（预留）
- **缓存机制**：筛选条件和搜索结果缓存

## 开发说明

### 自定义配置

可以通过修改 `js/config.js` 文件来调整系统配置：

```javascript
// 修改分页大小
CONFIG.pagination.defaultPageSize = 100;

// 修改自动刷新间隔
CONFIG.table.autoRefreshInterval = 60000; // 60秒

// 修改权限配置
PERMISSION_CONFIG.pageActions.export = false; // 禁用导出功能
```

### 扩展功能

1. **添加新的筛选条件**
   ```javascript
   // 在 FILTER_CONFIG 中添加新的筛选器
   FILTER_CONFIG.newFilter = {
       label: '新筛选器',
       condition: (ticket, user) => {
           // 筛选逻辑
           return true;
       }
   };
   ```

2. **添加新的操作按钮**
   ```javascript
   // 在 renderActionButtons 方法中添加新按钮
   if (customPermission) {
       buttons.push(`<button class="btn btn-xs btn-custom" data-action="custom">
           <i class="fas fa-custom"></i>
       </button>`);
   }
   ```

### 数据接口

系统预留了API接口配置，可以轻松对接后端服务：

```javascript
// API端点配置
CONFIG.api.endpoints = {
    getTickets: '/api/v1/tickets',
    createTicket: '/api/v1/tickets',
    updateTicket: '/api/v1/tickets/{id}',
    // ...
};
```

## 维护说明

### 代码规范
- 使用JSDoc注释
- 遵循ES6+语法规范
- 模块化设计，单一职责
- 错误处理和边界检查

### 调试技巧
1. 打开浏览器开发者工具
2. 查看控制台日志信息
3. 使用断点调试JavaScript
4. 检查网络请求状态

### 常见问题
1. **数据不显示**：检查模拟数据是否正确加载
2. **操作无响应**：检查权限配置和事件绑定
3. **样式异常**：检查CSS文件加载顺序
4. **性能问题**：检查数据量和渲染逻辑

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础工单列表功能
- 支持筛选、搜索、排序
- 实现批量操作和行内操作
- 响应式设计适配

---

**开发团队**：12345政务服务热线系统开发组  
**最后更新**：2024年1月  
**文档版本**：v1.0.0
