/**
 * 模拟数据文件
 * 包含工单列表、用户信息等演示数据
 */

// 生成随机工单编号
function generateTicketId() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    return `${year}${month}${day}${random}`;
}

// 生成随机时间
function generateRandomDate(daysAgo = 30) {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * daysAgo);
    const randomHours = Math.floor(Math.random() * 24);
    const randomMinutes = Math.floor(Math.random() * 60);
    
    const date = new Date(now);
    date.setDate(date.getDate() - randomDays);
    date.setHours(randomHours, randomMinutes, 0, 0);
    
    return date.toISOString();
}

// 生成剩余时间
function generateRemainingTime(createTime, status) {
    // 草稿状态和已关闭状态不显示剩余时间
    if (status === TICKET_STATUS.CLOSED || status === TICKET_STATUS.DRAFT) {
        return null;
    }
    
    const created = new Date(createTime);
    const now = new Date();
    const elapsed = now - created;
    
    // 根据工单类型设置不同的处理时限（小时）
    const timeLimit = 72; // 72小时
    const totalTime = timeLimit * 60 * 60 * 1000; // 转换为毫秒
    const remaining = totalTime - elapsed;
    
    if (remaining <= 0) {
        return {
            hours: 0,
            minutes: 0,
            percentage: 0,
            status: 'overdue'
        };
    }
    
    const remainingHours = Math.floor(remaining / (60 * 60 * 1000));
    const remainingMinutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));
    const percentage = Math.round((remaining / totalTime) * 100);
    
    let timeStatus = 'normal';
    if (percentage < 20) {
        timeStatus = 'danger';
    } else if (percentage < 50) {
        timeStatus = 'warning';
    }
    
    return {
        hours: remainingHours,
        minutes: remainingMinutes,
        percentage: percentage,
        status: timeStatus
    };
}

// 模拟工单数据
const MOCK_TICKETS = [
    {
        id: 'ticket001',
        woId: generateTicketId(),
        title: '小区垃圾清运不及时，影响居民生活',
        content: '我们小区的垃圾桶经常满溢，垃圾清运不及时，夏天气味很重，希望相关部门能够加强管理。',
        status: TICKET_STATUS.DRAFT,
        priority: PRIORITY_LEVEL.HIGH,
        type: TICKET_TYPE.COMPLAINT,
        channel: CHANNEL_TYPE.PHONE,
        citizenName: '王**',
        citizenPhone: '138****5678',
        citizenAddress: '朝阳区某某小区3号楼',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(1),
        responsibleUnit: null,
        returnCount: 2,  // 修改为被退回2次
        closeType: null,
        tags: ['环境卫生', '小区管理']
    },
    {
        id: 'ticket002',
        woId: generateTicketId(),
        title: '道路积水严重，影响通行',
        content: '下雨后道路积水严重，车辆和行人通行困难，请相关部门及时处理。',
        status: TICKET_STATUS.PENDING,
        priority: PRIORITY_LEVEL.URGENT,
        type: TICKET_TYPE.COMPLAINT,
        channel: CHANNEL_TYPE.APP,
        citizenName: '李**',
        citizenPhone: '139****1234',
        citizenAddress: '海淀区中关村大街',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(2),
        responsibleUnit: 'urban',
        returnCount: 0,
        closeType: null,
        tags: ['道路维护', '排水系统']
    },
    {
        id: 'ticket003',
        woId: generateTicketId(),
        title: '咨询社保转移手续办理流程',
        content: '我需要将社保从外地转移到本市，请问需要什么材料和手续？',
        status: TICKET_STATUS.CLOSED,
        priority: PRIORITY_LEVEL.NORMAL,
        type: TICKET_TYPE.CONSULTATION,
        channel: CHANNEL_TYPE.WEBSITE,
        citizenName: '陈**',
        citizenPhone: '137****9876',
        citizenAddress: '西城区某某街道',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(3),
        responsibleUnit: null,
        returnCount: 0,
        closeType: 'immediate',
        tags: ['社保咨询']
    },
    {
        id: 'ticket004',
        woId: generateTicketId(),
        title: '建议增设公交站点',
        content: '我们这个区域没有公交站点，居民出行不便，建议在附近增设公交站。',
        status: TICKET_STATUS.DRAFT,
        priority: PRIORITY_LEVEL.NORMAL,
        type: TICKET_TYPE.SUGGESTION,
        channel: CHANNEL_TYPE.WECHAT,
        citizenName: '刘**',
        citizenPhone: '136****5432',
        citizenAddress: '丰台区某某路',
        creatorId: 'user001',  // 修改为当前用户
        creatorName: '张三',   // 修改为当前用户
        createTime: generateRandomDate(1),
        responsibleUnit: null,
        returnCount: 1,
        closeType: null,
        tags: ['公共交通', '便民服务']
    },
    {
        id: 'ticket005',
        woId: generateTicketId(),
        title: '老人走失求助',
        content: '我家老人今天上午走失，身穿蓝色外套，患有轻度老年痴呆，请帮助寻找。',
        status: TICKET_STATUS.PROCESSING,
        priority: PRIORITY_LEVEL.URGENT,
        type: TICKET_TYPE.HELP,
        channel: CHANNEL_TYPE.PHONE,
        citizenName: '赵**',
        citizenPhone: '135****7890',
        citizenAddress: '东城区某某胡同',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(0),
        responsibleUnit: 'police',
        returnCount: 0,
        closeType: null,
        tags: ['紧急求助', '走失人员']
    },
    {
        id: 'ticket006',
        woId: generateTicketId(),
        title: '噪音扰民投诉',
        content: '楼下商铺夜间营业噪音很大，影响休息，多次沟通无效。',
        status: TICKET_STATUS.PENDING,
        priority: PRIORITY_LEVEL.HIGH,
        type: TICKET_TYPE.COMPLAINT,
        channel: CHANNEL_TYPE.APP,
        citizenName: '孙**',
        citizenPhone: '134****2468',
        citizenAddress: '石景山区某某小区',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(1),
        responsibleUnit: 'urban',
        returnCount: 0,
        closeType: null,
        tags: ['噪音污染', '商铺管理']
    },
    {
        id: 'ticket007',
        woId: generateTicketId(),
        title: '水费查询和缴费问题',
        content: '我想查询家里的水费使用情况，但是网上查不到，请问如何处理？',
        status: TICKET_STATUS.CLOSED,
        priority: PRIORITY_LEVEL.LOW,
        type: TICKET_TYPE.CONSULTATION,
        channel: CHANNEL_TYPE.PHONE,
        citizenName: '周**',
        citizenPhone: '133****1357',
        citizenAddress: '通州区某某小区',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(5),
        responsibleUnit: null,
        returnCount: 0,
        closeType: 'immediate',
        tags: ['水费查询', '便民服务']
    },
    {
        id: 'ticket008',
        woId: generateTicketId(),
        title: '学校周边交通安全隐患',
        content: '学校门口没有红绿灯，上下学时间车流量大，存在安全隐患。',
        status: TICKET_STATUS.DRAFT,
        priority: PRIORITY_LEVEL.HIGH,
        type: TICKET_TYPE.COMPLAINT,
        channel: CHANNEL_TYPE.EMAIL,
        citizenName: '吴**',
        citizenPhone: '132****9753',
        citizenAddress: '昌平区某某学校附近',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(2),
        responsibleUnit: null,
        returnCount: 3,  // 修改为被退回3次
        closeType: null,
        tags: ['交通安全', '学校周边']
    },
    {
        id: 'ticket008',
        woId: generateTicketId(),
        title: '菜市场环境卫生问题',
        content: '市民反映XX菜市场环境卫生状况较差，地面积水严重，垃圾清理不及时，摊位周围污水横流，异味严重，影响周边居民生活环境和食品安全。希望相关部门加强监管，改善菜市场卫生状况。',
        status: TICKET_STATUS.DRAFT,
        priority: PRIORITY_LEVEL.NORMAL,
        type: TICKET_TYPE.COMPLAINT,
        channel: CHANNEL_TYPE.WECHAT,
        citizenName: '陈**',
        citizenPhone: '137****9876',
        citizenAddress: '东城区XX街道XX菜市场附近',
        creatorId: 'user001',
        creatorName: '张三',
        createTime: generateRandomDate(2),
        responsibleUnit: null,
        returnCount: 2,  // 被退回2次，有历史追溯
        closeType: null,
        tags: ['环境卫生', '菜市场管理']
    }
];

// 为每个工单生成剩余时间
MOCK_TICKETS.forEach(ticket => {
    ticket.remainingTime = generateRemainingTime(ticket.createTime, ticket.status);
});

// 生成更多模拟数据
function generateMoreTickets(count = 50) {
    const additionalTickets = [];
    const titles = [
        '路灯不亮影响夜间出行',
        '小区健身器材损坏',
        '公园绿化带垃圾清理',
        '停车位划线不清晰',
        '下水道堵塞积水',
        '公交车站台破损',
        '社区活动场所申请',
        '老旧小区电梯故障',
        '菜市场环境卫生问题',
        '学区房政策咨询',
        '医保报销流程询问',
        '居住证办理指南',
        '噪音污染举报',
        '违章建筑投诉',
        '公共设施维修申请'
    ];
    
    const addresses = [
        '朝阳区某某小区',
        '海淀区某某街道',
        '西城区某某胡同',
        '东城区某某路',
        '丰台区某某园',
        '石景山区某某里',
        '通州区某某镇',
        '昌平区某某村',
        '大兴区某某庄',
        '房山区某某街'
    ];
    
    const creators = [
        { id: 'user001', name: '张三' },
        { id: 'user002', name: '李四' },
        { id: 'user003', name: '王五' },
        { id: 'user004', name: '赵六' }
    ];
    
    const statuses = [
        TICKET_STATUS.DRAFT,
        TICKET_STATUS.PENDING,
        TICKET_STATUS.PROCESSING,
        TICKET_STATUS.CLOSED
    ];
    
    const priorities = [
        PRIORITY_LEVEL.URGENT,
        PRIORITY_LEVEL.HIGH,
        PRIORITY_LEVEL.NORMAL,
        PRIORITY_LEVEL.LOW
    ];
    
    const types = [
        TICKET_TYPE.COMPLAINT,
        TICKET_TYPE.CONSULTATION,
        TICKET_TYPE.SUGGESTION,
        TICKET_TYPE.HELP
    ];
    
    const channels = [
        CHANNEL_TYPE.PHONE,
        CHANNEL_TYPE.WEBSITE,
        CHANNEL_TYPE.APP,
        CHANNEL_TYPE.WECHAT,
        CHANNEL_TYPE.EMAIL
    ];
    
    const units = [
        'urban', 'transport', 'housing', 'education', 
        'health', 'environment', 'water', 'emergency'
    ];
    
    for (let i = 0; i < count; i++) {
        const creator = creators[Math.floor(Math.random() * creators.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const createTime = generateRandomDate(30);
        
        const ticket = {
            id: `ticket${String(MOCK_TICKETS.length + i + 1).padStart(3, '0')}`,
            woId: generateTicketId(),
            title: titles[Math.floor(Math.random() * titles.length)],
            content: '这是一个模拟的工单内容描述...',
            status: status,
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            type: types[Math.floor(Math.random() * types.length)],
            channel: channels[Math.floor(Math.random() * channels.length)],
            citizenName: `市民${String.fromCharCode(65 + Math.floor(Math.random() * 26))}**`,
            citizenPhone: `1${Math.floor(Math.random() * 9) + 3}${Math.floor(Math.random() * 10)}****${Math.floor(Math.random() * 9000) + 1000}`,
            citizenAddress: addresses[Math.floor(Math.random() * addresses.length)],
            creatorId: creator.id,
            creatorName: creator.name,
            createTime: createTime,
            responsibleUnit: status === TICKET_STATUS.PENDING || status === TICKET_STATUS.PROCESSING 
                ? units[Math.floor(Math.random() * units.length)] 
                : null,
            returnCount: Math.random() > 0.8 ? Math.floor(Math.random() * 3) : 0,
            closeType: status === TICKET_STATUS.CLOSED ? 'immediate' : null,
            tags: ['模拟数据']
        };
        
        ticket.remainingTime = generateRemainingTime(ticket.createTime, ticket.status);
        additionalTickets.push(ticket);
    }
    
    return additionalTickets;
}

// 合并所有工单数据
const ALL_TICKETS = [...MOCK_TICKETS, ...generateMoreTickets(42)];

// 模拟API响应数据结构
const MOCK_API_RESPONSE = {
    success: true,
    data: {
        tickets: ALL_TICKETS,
        total: ALL_TICKETS.length,
        page: 1,
        pageSize: 50
    },
    message: '获取成功'
};

// 统计数据
const MOCK_STATISTICS = {
    todayCount: ALL_TICKETS.filter(ticket => {
        const today = new Date().toDateString();
        return new Date(ticket.createTime).toDateString() === today;
    }).length,
    
    pendingCount: ALL_TICKETS.filter(ticket => 
        ticket.status === TICKET_STATUS.DRAFT || ticket.status === TICKET_STATUS.PENDING
    ).length,
    
    myDraftsCount: ALL_TICKETS.filter(ticket => 
        ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === CURRENT_USER.id
    ).length,
    
    returnedCount: ALL_TICKETS.filter(ticket => 
        ticket.status === TICKET_STATUS.DRAFT && ticket.returnCount > 0 && ticket.creatorId === CURRENT_USER.id
    ).length,
    
    assignedCount: ALL_TICKETS.filter(ticket => 
        ticket.status === TICKET_STATUS.PENDING
    ).length,
    
    closedCount: ALL_TICKETS.filter(ticket => 
        ticket.status === TICKET_STATUS.CLOSED && ticket.closeType === 'immediate' && ticket.creatorId === CURRENT_USER.id
    ).length,
    
    urgentCount: ALL_TICKETS.filter(ticket => 
        ticket.priority === PRIORITY_LEVEL.URGENT || ticket.priority === PRIORITY_LEVEL.HIGH
    ).length,
    
    allCount: ALL_TICKETS.length
};

// 导出数据
window.MockData = {
    tickets: ALL_TICKETS,
    statistics: MOCK_STATISTICS,
    apiResponse: MOCK_API_RESPONSE
};
