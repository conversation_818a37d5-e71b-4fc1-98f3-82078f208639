/**
 * 表单验证模块
 * 包含各种表单字段的验证规则和验证函数
 */

class FormValidator {
    constructor() {
        this.rules = new Map();
        this.errors = new Map();
        this.initDefaultRules();
    }

    /**
     * 初始化默认验证规则
     */
    initDefaultRules() {
        // 必填验证
        this.addRule('required', (value, field) => {
            if (value === null || value === undefined || value === '') {
                return `${field}不能为空`;
            }
            return null;
        });

        // 长度验证
        this.addRule('minLength', (value, field, minLength) => {
            if (value && value.length < minLength) {
                return `${field}至少需要${minLength}个字符`;
            }
            return null;
        });

        this.addRule('maxLength', (value, field, maxLength) => {
            if (value && value.length > maxLength) {
                return `${field}不能超过${maxLength}个字符`;
            }
            return null;
        });

        // 手机号验证
        this.addRule('phone', (value, field) => {
            if (value && !CONFIG.validation.phone.test(value)) {
                return `请输入正确的手机号码`;
            }
            return null;
        });

        // 座机号验证
        this.addRule('landline', (value, field) => {
            if (value && !CONFIG.validation.landline.test(value)) {
                return `请输入正确的座机号码`;
            }
            return null;
        });

        // 电话号码验证（手机或座机）
        this.addRule('telephone', (value, field) => {
            if (value && !CONFIG.validation.phone.test(value) && !CONFIG.validation.landline.test(value)) {
                return `请输入正确的电话号码`;
            }
            return null;
        });

        // 身份证验证
        this.addRule('idCard', (value, field) => {
            if (value && !CONFIG.validation.idCard.test(value)) {
                return `请输入正确的身份证号码`;
            }
            return null;
        });

        // 邮箱验证
        this.addRule('email', (value, field) => {
            if (value && !CONFIG.validation.email.test(value)) {
                return `请输入正确的邮箱地址`;
            }
            return null;
        });

        // 邮编验证
        this.addRule('zipCode', (value, field) => {
            if (value && !CONFIG.validation.zipCode.test(value)) {
                return `请输入正确的邮政编码`;
            }
            return null;
        });

        // 中文姓名验证
        this.addRule('chineseName', (value, field) => {
            if (value && !CONFIG.validation.chineseName.test(value)) {
                return `请输入正确的中文姓名`;
            }
            return null;
        });

        // 数字范围验证
        this.addRule('numberRange', (value, field, min, max) => {
            const num = parseFloat(value);
            if (isNaN(num)) {
                return `${field}必须是数字`;
            }
            if (num < min || num > max) {
                return `${field}必须在${min}到${max}之间`;
            }
            return null;
        });

        // 选择验证
        this.addRule('selected', (value, field) => {
            if (!value || (Array.isArray(value) && value.length === 0)) {
                return `请选择${field}`;
            }
            return null;
        });

        // 文件验证
        this.addRule('file', (files, field, options = {}) => {
            if (!files || files.length === 0) {
                if (options.required) {
                    return `请选择${field}`;
                }
                return null;
            }

            const { maxCount = 10, maxSize = CONFIG.form.maxFileSize } = options;
            
            if (files.length > maxCount) {
                return `最多只能上传${maxCount}个文件`;
            }

            let totalSize = 0;
            for (const file of files) {
                const validation = UTILS.validateFile(file);
                if (!validation.valid) {
                    return validation.message;
                }
                totalSize += file.size;
            }

            if (totalSize > maxSize) {
                return `文件总大小不能超过${UTILS.formatFileSize(maxSize)}`;
            }

            return null;
        });
    }

    /**
     * 添加验证规则
     * @param {string} name 规则名称
     * @param {Function} validator 验证函数
     */
    addRule(name, validator) {
        this.rules.set(name, validator);
    }

    /**
     * 验证单个字段
     * @param {string} fieldName 字段名称
     * @param {any} value 字段值
     * @param {Array} rules 验证规则
     * @param {string} displayName 显示名称
     * @returns {string|null} 错误信息或null
     */
    validateField(fieldName, value, rules, displayName = fieldName) {
        for (const rule of rules) {
            let ruleName, ruleParams = [];
            
            if (typeof rule === 'string') {
                ruleName = rule;
            } else if (Array.isArray(rule)) {
                [ruleName, ...ruleParams] = rule;
            } else if (typeof rule === 'object') {
                ruleName = rule.name;
                ruleParams = rule.params || [];
            }

            const validator = this.rules.get(ruleName);
            if (!validator) {
                console.warn(`验证规则 ${ruleName} 不存在`);
                continue;
            }

            const error = validator(value, displayName, ...ruleParams);
            if (error) {
                this.errors.set(fieldName, error);
                return error;
            }
        }

        this.errors.delete(fieldName);
        return null;
    }

    /**
     * 验证表单数据
     * @param {Object} formData 表单数据
     * @param {Object} validationRules 验证规则配置
     * @returns {Object} 验证结果
     */
    validateForm(formData, validationRules) {
        const errors = {};
        let isValid = true;

        for (const [fieldName, rules] of Object.entries(validationRules)) {
            const { rules: fieldRules, displayName, condition } = rules;
            
            // 检查条件是否满足
            if (condition && !condition(formData)) {
                continue;
            }

            const value = formData[fieldName];
            const error = this.validateField(fieldName, value, fieldRules, displayName);
            
            if (error) {
                errors[fieldName] = error;
                isValid = false;
            }
        }

        return { isValid, errors };
    }

    /**
     * 获取字段错误信息
     * @param {string} fieldName 字段名称
     * @returns {string|null} 错误信息
     */
    getFieldError(fieldName) {
        return this.errors.get(fieldName) || null;
    }

    /**
     * 清除字段错误
     * @param {string} fieldName 字段名称
     */
    clearFieldError(fieldName) {
        this.errors.delete(fieldName);
    }

    /**
     * 清除所有错误
     */
    clearAllErrors() {
        this.errors.clear();
    }

    /**
     * 获取所有错误
     * @returns {Object} 所有错误信息
     */
    getAllErrors() {
        return Object.fromEntries(this.errors);
    }
}

// 表单验证规则配置
const FORM_VALIDATION_RULES = {
    // 工单来源
    ticketSource: {
        rules: ['required'],
        displayName: '工单来源'
    },
    
    // 来电号码
    phoneNumber: {
        rules: ['telephone'],
        displayName: '来电号码'
    },
    
    // 工单类型
    ticketType: {
        rules: ['required'],
        displayName: '工单类型'
    },
    
    // 紧急程度
    priority: {
        rules: ['required'],
        displayName: '紧急程度'
    },
    
    // 处理模式
    processMode: {
        rules: ['required'],
        displayName: '处理模式'
    },
    
    // 承办单位
    selectedDepartments: {
        rules: ['selected'],
        displayName: '承办单位',
        condition: (data) => data.processMode === '派单处理'
    },

    // 主办单位
    mainDepartment: {
        rules: ['required'],
        displayName: '主办单位',
        condition: (data) => data.processMode === '派单处理' && data.selectedDepartments && data.selectedDepartments.length > 1
    },
    
    // 接单时限（仅派单处理模式需要）
    acceptDeadline: {
        rules: ['required', ['numberRange', 0.5, 5]],
        displayName: '接单时限',
        condition: (data) => data.processMode === '派单处理'
    },

    // 办结时限（仅派单处理模式需要）
    finishDeadline: {
        rules: ['required', ['numberRange', 1, 60]],
        displayName: '办结时限',
        condition: (data) => data.processMode === '派单处理'
    },
    
    // 诉求标题
    title: {
        rules: ['required', ['minLength', 5], ['maxLength', 100]],
        displayName: '诉求标题'
    },
    
    // 详细描述
    description: {
        rules: ['required', ['minLength', 20], ['maxLength', 2000]],
        displayName: '详细描述'
    },
    
    // 一级分类
    category1: {
        rules: ['required'],
        displayName: '一级分类'
    },
    
    // 二级分类
    category2: {
        rules: ['required'],
        displayName: '二级分类'
    },
    
    // 省份
    province: {
        rules: ['required'],
        displayName: '省份'
    },

    // 城市
    city: {
        rules: ['required'],
        displayName: '城市'
    },

    // 区县
    district: {
        rules: ['required'],
        displayName: '区/县'
    },
    
    // 市民姓名
    citizenName: {
        rules: ['required', 'chineseName'],
        displayName: '市民姓名'
    },
    
    // 联系电话
    citizenPhone: {
        rules: ['required', 'phone'],
        displayName: '联系电话'
    },
    
    // 身份证号
    idCard: {
        rules: ['idCard'],
        displayName: '身份证号'
    },
    
    // 备用电话
    backupPhone: {
        rules: ['telephone'],
        displayName: '备用电话'
    },
    
    // 电子邮箱
    email: {
        rules: ['email'],
        displayName: '电子邮箱'
    },
    
    // 邮政编码
    zipCode: {
        rules: ['zipCode'],
        displayName: '邮政编码'
    },
    
    // 客户类型
    citizenType: {
        rules: ['required'],
        displayName: '客户类型'
    },
    
    // 办结说明
    finishNote: {
        rules: ['required', ['minLength', 10], ['maxLength', 500]],
        displayName: '办结说明',
        condition: (data) => data.processMode === '即时办结'
    }
};

// 实时验证配置
const REAL_TIME_VALIDATION = {
    // 输入时验证（防抖）
    onInput: ['title', 'description', 'address', 'citizenName', 'finishNote'],
    
    // 失焦时验证
    onBlur: ['phoneNumber', 'citizenPhone', 'backupPhone', 'email', 'idCard', 'zipCode'],
    
    // 改变时验证
    onChange: ['ticketSource', 'ticketType', 'priority', 'processMode', 'department', 'category1', 'category2', 'citizenType']
};

// 创建全局验证器实例
const formValidator = new FormValidator();

// 导出模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        FormValidator,
        FORM_VALIDATION_RULES,
        REAL_TIME_VALIDATION,
        formValidator
    };
}
