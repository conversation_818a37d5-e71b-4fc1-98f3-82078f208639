/**
 * 表单处理模块
 * 包含表单数据处理、提交、保存等核心逻辑
 */

class FormHandler {
    constructor() {
        this.formData = {};
        this.isDirty = false;
        this.autoSaveTimer = null;
        this.isSubmitting = false;
        
        this.initFormHandler();
    }

    /**
     * 初始化表单处理器
     */
    initFormHandler() {
        this.initFormElements();
        this.initAutoSave();
        this.initFormValidation();
        this.initFormActions();
        this.initBeforeUnload();
        this.loadDraftData();
        this.initCurrentTime();
    }

    /**
     * 初始化表单元素
     */
    initFormElements() {
        const form = document.getElementById('ticketForm');
        
        // 监听表单变化
        form.addEventListener('input', (e) => {
            this.handleFormChange(e);
        });

        form.addEventListener('change', (e) => {
            this.handleFormChange(e);
        });

        // 工单类型变化时更新默认时限
        const ticketTypeInputs = document.querySelectorAll('input[name="ticketType"]');
        ticketTypeInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.updateDefaultDeadlines(e.target.value);
                }
            });
        });

        // 页面加载时设置默认时限（如果已有选中的工单类型）
        const checkedTicketType = document.querySelector('input[name="ticketType"]:checked');
        if (checkedTicketType) {
            this.updateDefaultDeadlines(checkedTicketType.value);
        }

        // 时限变化时显示特殊说明
        const acceptDeadlineInput = document.getElementById('acceptDeadline');
        const finishDeadlineInput = document.getElementById('finishDeadline');

        [acceptDeadlineInput, finishDeadlineInput].forEach(input => {
            input.addEventListener('input', () => {
                this.checkSpecialDeadline();
            });
        });

        // 处理模式变化时重新检查时限
        const processModeInputs = document.querySelectorAll('input[name="processMode"]');
        processModeInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.checkSpecialDeadline();
            });
        });

        // 联系电话重复检测
        const citizenPhoneInput = document.getElementById('citizenPhone');
        citizenPhoneInput.addEventListener('blur', () => {
            this.checkRepeatPhone(citizenPhoneInput.value);
        });
    }

    /**
     * 处理表单变化
     */
    handleFormChange(e) {
        const { name, value, type, checked } = e.target;
        
        if (type === 'checkbox') {
            this.updateFormData(name, checked);
        } else if (type === 'radio') {
            if (checked) {
                this.updateFormData(name, value);
            }
        } else {
            this.updateFormData(name || e.target.id, value);
        }

        this.markAsDirty();
        
        // 实时验证
        this.performRealTimeValidation(e.target);
    }

    /**
     * 更新表单数据
     */
    updateFormData(key, value) {
        if (key) {
            this.formData[key] = value;
        }
    }

    /**
     * 标记表单为已修改
     */
    markAsDirty() {
        this.isDirty = true;
        this.updateFormStatus('草稿');
    }

    /**
     * 更新表单状态
     */
    updateFormStatus(status) {
        const statusIndicator = document.querySelector('.status-indicator');
        if (statusIndicator) {
            statusIndicator.textContent = status;
            statusIndicator.className = `status-indicator ${status.toLowerCase()}`;
        }
    }

    /**
     * 更新默认时限
     */
    updateDefaultDeadlines(ticketType) {
        const acceptDeadlineInput = document.getElementById('acceptDeadline');
        const finishDeadlineInput = document.getElementById('finishDeadline');

        const defaultAccept = CONSTANTS.DEFAULT_DEADLINES.accept[ticketType];
        const defaultFinish = CONSTANTS.DEFAULT_DEADLINES.finish[ticketType];

        console.log('更新默认时限:', {
            ticketType: ticketType,
            defaultAccept: defaultAccept,
            defaultFinish: defaultFinish,
            currentAccept: acceptDeadlineInput.value,
            currentFinish: finishDeadlineInput.value
        });

        // 设置默认接单时限
        if (defaultAccept !== undefined) {
            acceptDeadlineInput.value = defaultAccept;
            this.updateFormData('acceptDeadline', defaultAccept);

            // 显示提示信息
            this.showDeadlineUpdateMessage('接单时限', defaultAccept, '工作日');
        }

        // 设置默认办结时限
        if (defaultFinish !== undefined) {
            finishDeadlineInput.value = defaultFinish;
            this.updateFormData('finishDeadline', defaultFinish);

            // 显示提示信息
            this.showDeadlineUpdateMessage('办结时限', defaultFinish, '工作日');
        }

        // 检查是否需要显示特殊时限说明
        this.checkSpecialDeadline();
    }

    /**
     * 显示时限更新提示
     */
    showDeadlineUpdateMessage(type, value, unit) {
        const message = `${type}已自动设置为 ${value} ${unit}`;
        uiComponents.showMessage(message, 'info');
    }

    /**
     * 检查特殊时限
     */
    checkSpecialDeadline() {
        const processMode = document.querySelector('input[name="processMode"]:checked')?.value;
        const specialDeadlineGroup = document.getElementById('specialDeadlineGroup');

        // 即时办结模式下不需要检查时限
        if (processMode === '即时办结') {
            if (specialDeadlineGroup) {
                specialDeadlineGroup.style.display = 'none';
            }
            return;
        }

        const acceptDeadline = parseFloat(document.getElementById('acceptDeadline').value);
        const finishDeadline = parseFloat(document.getElementById('finishDeadline').value);
        const ticketType = document.querySelector('input[name="ticketType"]:checked')?.value;

        if (!ticketType || !specialDeadlineGroup) return;

        const defaultAccept = CONSTANTS.DEFAULT_DEADLINES.accept[ticketType];
        const defaultFinish = CONSTANTS.DEFAULT_DEADLINES.finish[ticketType];

        // 检查是否与默认值不同
        const isAcceptSpecial = !isNaN(acceptDeadline) && acceptDeadline !== defaultAccept;
        const isFinishSpecial = !isNaN(finishDeadline) && finishDeadline !== defaultFinish;
        const isSpecial = isAcceptSpecial || isFinishSpecial;

        console.log('检查特殊时限:', {
            processMode: processMode,
            ticketType: ticketType,
            acceptDeadline: acceptDeadline,
            finishDeadline: finishDeadline,
            defaultAccept: defaultAccept,
            defaultFinish: defaultFinish,
            isSpecial: isSpecial
        });

        if (isSpecial) {
            specialDeadlineGroup.style.display = 'block';

            // 自动填写说明
            const specialNote = document.getElementById('specialDeadlineNote');
            if (!specialNote.value) {
                let reasons = [];
                if (isAcceptSpecial) {
                    reasons.push(`接单时限调整为${acceptDeadline}个工作日`);
                }
                if (isFinishSpecial) {
                    reasons.push(`办结时限调整为${finishDeadline}个工作日`);
                }
                specialNote.value = reasons.join('，') + '。';
                this.updateFormData('specialDeadlineNote', specialNote.value);
            }
        } else {
            specialDeadlineGroup.style.display = 'none';
            // 清空特殊说明
            const specialNote = document.getElementById('specialDeadlineNote');
            if (specialNote) {
                specialNote.value = '';
                this.updateFormData('specialDeadlineNote', '');
            }
        }
    }

    /**
     * 检查重复电话
     */
    checkRepeatPhone(phone) {
        const repeatWarning = document.getElementById('repeatPhoneWarning');
        
        // 模拟检查重复电话（实际应该调用API）
        const isRepeat = Math.random() < 0.3; // 30%概率显示重复警告
        
        if (phone && CONFIG.validation.phone.test(phone) && isRepeat) {
            repeatWarning.style.display = 'flex';
        } else {
            repeatWarning.style.display = 'none';
        }
    }

    /**
     * 初始化自动保存
     */
    initAutoSave() {
        this.autoSaveTimer = setInterval(() => {
            if (this.isDirty && !this.isSubmitting) {
                this.saveDraft(true);
            }
        }, CONFIG.form.autoSaveInterval);
    }

    /**
     * 初始化表单验证
     */
    initFormValidation() {
        // 实时验证配置
        REAL_TIME_VALIDATION.onInput.forEach(fieldName => {
            const element = document.getElementById(fieldName);
            if (element) {
                const debouncedValidation = UTILS.debounce(() => {
                    this.validateField(fieldName);
                }, 500);
                element.addEventListener('input', debouncedValidation);
            }
        });

        REAL_TIME_VALIDATION.onBlur.forEach(fieldName => {
            const element = document.getElementById(fieldName);
            if (element) {
                element.addEventListener('blur', () => {
                    this.validateField(fieldName);
                });
            }
        });

        REAL_TIME_VALIDATION.onChange.forEach(fieldName => {
            const elements = document.querySelectorAll(`[name="${fieldName}"], #${fieldName}`);
            elements.forEach(element => {
                element.addEventListener('change', () => {
                    this.validateField(fieldName);
                });
            });
        });
    }

    /**
     * 执行实时验证
     */
    performRealTimeValidation(element) {
        const fieldName = element.name || element.id;
        if (REAL_TIME_VALIDATION.onInput.includes(fieldName) || 
            REAL_TIME_VALIDATION.onBlur.includes(fieldName) || 
            REAL_TIME_VALIDATION.onChange.includes(fieldName)) {
            this.validateField(fieldName);
        }
    }

    /**
     * 验证单个字段
     */
    validateField(fieldName) {
        const rules = FORM_VALIDATION_RULES[fieldName];
        if (!rules) return;

        const value = this.getFieldValue(fieldName);
        const error = formValidator.validateField(fieldName, value, rules.rules, rules.displayName);
        
        this.showFieldError(fieldName, error);
        return !error;
    }

    /**
     * 获取字段值
     */
    getFieldValue(fieldName) {
        // 特殊处理选中的部门
        if (fieldName === 'selectedDepartments') {
            return Array.from(uiComponents.selectedDepartments);
        }

        const element = document.getElementById(fieldName) || document.querySelector(`[name="${fieldName}"]`);

        if (!element) return null;

        if (element.type === 'radio') {
            const checked = document.querySelector(`[name="${fieldName}"]:checked`);
            return checked ? checked.value : null;
        } else if (element.type === 'checkbox') {
            if (element.name) {
                // 多选框组
                const checked = document.querySelectorAll(`[name="${fieldName}"]:checked`);
                return Array.from(checked).map(cb => cb.value);
            } else {
                // 单个复选框
                return element.checked;
            }
        } else {
            return element.value;
        }
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldName, error) {
        // 特殊处理部门选择错误显示
        if (fieldName === 'selectedDepartments') {
            fieldName = 'department';
        }

        const errorElement = document.getElementById(`${fieldName}Error`);
        const formGroup = document.getElementById('departmentGroup') || document.getElementById(fieldName)?.closest('.form-group');

        if (errorElement) {
            if (error) {
                errorElement.textContent = error;
                errorElement.classList.add('show');
            } else {
                errorElement.classList.remove('show');
            }
        }

        if (formGroup) {
            if (error) {
                formGroup.classList.add('has-error');
                formGroup.classList.remove('has-success');
            } else {
                formGroup.classList.remove('has-error');
                formGroup.classList.add('has-success');
            }
        }
    }

    /**
     * 初始化表单操作
     */
    initFormActions() {
        // 重置表单
        document.getElementById('resetForm').addEventListener('click', () => {
            this.showConfirmDialog('确定要重置表单吗？', '所有已填写的内容将被清空', () => {
                this.resetForm();
            });
        });

        // 暂存草稿
        document.getElementById('saveDraft').addEventListener('click', () => {
            this.saveDraft();
        });

        // 提交工单
        document.getElementById('submitTicket').addEventListener('click', (e) => {
            e.preventDefault();
            this.submitTicket();
        });
    }

    /**
     * 重置表单
     */
    resetForm() {
        const form = document.getElementById('ticketForm');
        form.reset();
        
        // 清除自定义数据
        this.formData = {};
        uiComponents.selectedBusinessTags.clear();
        uiComponents.customTags = [];
        uiComponents.attachments = [];
        
        // 重新渲染组件
        uiComponents.renderCustomTags();
        uiComponents.renderAttachments();
        
        // 清除验证错误
        formValidator.clearAllErrors();
        document.querySelectorAll('.form-error').forEach(el => el.classList.remove('show'));
        document.querySelectorAll('.form-group').forEach(el => {
            el.classList.remove('has-error', 'has-success');
        });
        
        this.isDirty = false;
        this.updateFormStatus('新建');
        
        // 重新初始化时间
        this.initCurrentTime();
        
        uiComponents.showMessage('表单已重置', 'success');
    }



    /**
     * 保存草稿
     */
    async saveDraft(isAutoSave = false) {
        if (this.isSubmitting) return;
        
        try {
            const formData = this.collectFormData();
            
            if (!isAutoSave) {
                this.showLoadingOverlay('保存中...');
            }
            
            const response = await MockAPI.saveDraft(formData);
            
            if (response.success) {
                this.isDirty = false;
                this.updateFormStatus('已保存');
                this.updateAutoSaveStatus('已保存');
                
                // 保存到本地存储
                UTILS.storage.set(CONSTANTS.STORAGE_KEYS.DRAFT_DATA, formData);
                
                if (!isAutoSave) {
                    uiComponents.showMessage('草稿保存成功', 'success');
                }
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            console.error('保存草稿失败:', error);
            if (!isAutoSave) {
                uiComponents.showMessage('保存失败，请重试', 'error');
            }
        } finally {
            if (!isAutoSave) {
                this.hideLoadingOverlay();
            }
        }
    }

    /**
     * 提交工单
     */
    async submitTicket() {
        if (this.isSubmitting) return;
        
        // 验证表单
        const formData = this.collectFormData();
        const validation = formValidator.validateForm(formData, FORM_VALIDATION_RULES);
        
        if (!validation.isValid) {
            this.showValidationErrors(validation.errors);
            uiComponents.showMessage('请检查表单填写', 'error');
            return;
        }
        
        // 确认提交
        this.showConfirmDialog(
            '确定要提交工单吗？',
            '提交后将无法修改，请确认信息无误',
            async () => {
                await this.performSubmit(formData);
            }
        );
    }

    /**
     * 执行提交
     */
    async performSubmit(formData) {
        this.isSubmitting = true;
        
        try {
            this.showLoadingOverlay('提交中...');
            
            const response = await MockAPI.submitTicket(formData);
            
            if (response.success) {
                this.updateFormStatus('已提交');
                this.isDirty = false;
                
                // 清除草稿数据
                UTILS.storage.remove(CONSTANTS.STORAGE_KEYS.DRAFT_DATA);
                
                uiComponents.showMessage('工单提交成功', 'success');
                
                // 可以跳转到成功页面或显示成功信息
                setTimeout(() => {
                    this.showSuccessPage(response.data);
                }, 1000);
            } else {
                throw new Error(response.message || '提交失败');
            }
        } catch (error) {
            console.error('提交工单失败:', error);
            uiComponents.showMessage('提交失败，请重试', 'error');
        } finally {
            this.hideLoadingOverlay();
            this.isSubmitting = false;
        }
    }

    /**
     * 显示验证错误
     */
    showValidationErrors(errors) {
        for (const [fieldName, error] of Object.entries(errors)) {
            this.showFieldError(fieldName, error);
        }
        
        // 滚动到第一个错误字段
        const firstErrorField = Object.keys(errors)[0];
        const firstErrorElement = document.getElementById(firstErrorField);
        if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorElement.focus();
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const form = document.getElementById('ticketForm');
        const formData = new FormData(form);
        const data = {};
        
        // 基础表单数据
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // 添加额外数据
        data.businessTags = Array.from(uiComponents.selectedBusinessTags);
        data.customTags = uiComponents.customTags;
        data.attachments = uiComponents.attachments;
        data.selectedDepartments = Array.from(uiComponents.selectedDepartments);

        // 添加完整地址信息
        if (uiComponents.getSelectedAddressData) {
            data.addressData = uiComponents.getSelectedAddressData();
            data.fullAddress = MockAPI.buildFullAddress(data.addressData);
        }

        return data;
    }

    /**
     * 加载草稿数据
     */
    loadDraftData() {
        const draftData = UTILS.storage.get(CONSTANTS.STORAGE_KEYS.DRAFT_DATA);
        if (draftData) {
            this.populateForm(draftData);
            this.updateFormStatus('草稿');
            uiComponents.showMessage('已加载草稿数据', 'info');
        }
    }

    /**
     * 填充表单数据
     */
    populateForm(data) {
        for (const [key, value] of Object.entries(data)) {
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'radio') {
                    const radio = document.querySelector(`[name="${key}"][value="${value}"]`);
                    if (radio) radio.checked = true;
                } else if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        }
        
        // 恢复业务标签
        if (data.businessTags) {
            data.businessTags.forEach(tag => {
                uiComponents.selectedBusinessTags.add(tag);
                const tagElement = document.querySelector(`[data-value="${tag}"]`);
                if (tagElement) tagElement.classList.add('selected');
            });
        }
        
        // 恢复自定义标签
        if (data.customTags) {
            uiComponents.customTags = data.customTags;
            uiComponents.renderCustomTags();
        }
    }

    /**
     * 初始化当前时间
     */
    initCurrentTime() {
        const createTimeInput = document.getElementById('createTime');
        if (createTimeInput) {
            createTimeInput.value = UTILS.formatDateTime();
        }

        // 初始化默认时限（如果没有选择工单类型，使用投诉类的默认值）
        this.initDefaultDeadlines();
    }

    /**
     * 初始化默认时限
     */
    initDefaultDeadlines() {
        const acceptDeadlineInput = document.getElementById('acceptDeadline');
        const finishDeadlineInput = document.getElementById('finishDeadline');

        // 如果时限输入框为空，设置默认值（使用投诉类的默认值）
        if (!acceptDeadlineInput.value) {
            const defaultAccept = CONSTANTS.DEFAULT_DEADLINES.accept['投诉'];
            acceptDeadlineInput.value = defaultAccept;
            acceptDeadlineInput.placeholder = `默认 ${defaultAccept} 工作日`;
            this.updateFormData('acceptDeadline', defaultAccept);
        }

        if (!finishDeadlineInput.value) {
            const defaultFinish = CONSTANTS.DEFAULT_DEADLINES.finish['投诉'];
            finishDeadlineInput.value = defaultFinish;
            finishDeadlineInput.placeholder = `默认 ${defaultFinish} 工作日`;
            this.updateFormData('finishDeadline', defaultFinish);
        }
    }

    /**
     * 更新自动保存状态
     */
    updateAutoSaveStatus(status) {
        const autoSaveStatus = document.querySelector('.auto-save-status');
        if (autoSaveStatus) {
            autoSaveStatus.textContent = status;
        }
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, onConfirm) {
        const modal = document.getElementById('confirmModal');
        const titleEl = document.getElementById('confirmTitle');
        const messageEl = document.getElementById('confirmMessage');
        const confirmBtn = document.getElementById('confirmOk');
        const cancelBtn = document.getElementById('confirmCancel');
        
        titleEl.textContent = title;
        messageEl.textContent = message;
        
        const handleConfirm = () => {
            uiComponents.hideModal(modal);
            onConfirm();
            confirmBtn.removeEventListener('click', handleConfirm);
        };
        
        const handleCancel = () => {
            uiComponents.hideModal(modal);
            confirmBtn.removeEventListener('click', handleConfirm);
        };
        
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        
        uiComponents.showModal(modal);
    }

    /**
     * 显示加载遮罩
     */
    showLoadingOverlay(message = '加载中...') {
        let overlay = document.getElementById('loadingOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-message">${message}</div>
            `;
            document.body.appendChild(overlay);
        }
        
        overlay.querySelector('.loading-message').textContent = message;
        overlay.classList.add('show');
    }

    /**
     * 隐藏加载遮罩
     */
    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    /**
     * 显示成功页面
     */
    showSuccessPage(data) {
        // 这里可以跳转到成功页面或显示成功信息
        alert(`工单提交成功！\n工单编号：${data.ticketId}\n提交时间：${data.submitTime}`);
    }

    /**
     * 初始化页面离开前确认
     */
    initBeforeUnload() {
        window.addEventListener('beforeunload', (e) => {
            if (this.isDirty && !this.isSubmitting) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开吗？';
                return e.returnValue;
            }
        });
    }

    /**
     * 销毁表单处理器
     */
    destroy() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
    }
}

// 创建全局表单处理器实例
const formHandler = new FormHandler();

// 导出模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FormHandler, formHandler };
}
