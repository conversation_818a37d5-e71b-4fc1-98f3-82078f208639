/**
 * 响应式样式文件
 * 适配不同屏幕尺寸的样式调整
 */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) {
    .main-content {
        padding: 16px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }
    
    .search-container {
        min-width: 250px;
    }
    
    .form-row {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    
    .header-stats {
        gap: 12px;
    }
    
    .stat-item {
        min-width: 60px;
    }
}

/* 现代化响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 16px 20px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }
    
    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .header-left {
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }
    
    .page-title {
        font-size: 20px;
        font-weight: 700;
    }
    
    .user-info {
        text-align: center;
    }
    
    .header-right {
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
    }
    
    .main-content {
        padding: 16px 20px;
    }
    
    .toolbar {
        padding: 16px 20px;
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .toolbar-left {
        flex-direction: column;
        gap: 12px;
    }
    
    .search-container {
        min-width: 100%;
        width: 100%;
    }
    
    .search-input {
        width: 100%;
        padding: 14px 48px 14px 16px;
    }
    
    .batch-actions {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .filter-tabs {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        border-radius: var(--radius-lg);
    }
    
    .filter-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .tab-item {
        min-width: 100px;
        padding: 16px 20px;
        justify-content: flex-start;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .tab-item.active {
        border-bottom: 1px solid var(--primary-color);
        border-left: 4px solid var(--primary-color);
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .advanced-search-panel {
        margin-bottom: 16px;
    }
    
    .search-form {
        padding: 16px 20px;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 12px;
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    .table-container {
        overflow-x: auto;
        border-radius: var(--radius-lg);
        margin-bottom: 16px;
    }
    
    .ticket-table {
        min-width: 900px;
        font-size: 13px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 12px 16px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .action-buttons .btn {
        font-size: 11px;
        padding: 8px 10px;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 16px;
        align-items: center;
        padding: 16px 20px;
    }
    
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .page-btn {
        min-width: 44px;
        height: 44px;
        padding: 10px 14px;
        font-size: 14px;
    }
    
    .modal {
        max-width: 95vw;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px 20px;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 12px;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .page-title {
        font-size: 18px;
        font-weight: 700;
    }
    
    .page-title i {
        display: none;
    }
    
    .header-stats {
        gap: 12px;
    }
    
    .stat-item {
        min-width: 140px;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-lg);
        backdrop-filter: blur(10px);
        text-align: center;
    }
    
    .stat-value {
        font-size: 18px;
        font-weight: 700;
    }
    
    .main-content {
        padding: 12px 16px;
    }
    
    .toolbar {
        padding: 12px 16px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 14px;
        font-weight: 600;
    }
    
    .btn-icon {
        padding: 12px;
        min-width: 40px;
    }
    
    .tab-item {
        min-width: 80px;
        padding: 14px 16px;
        font-size: 14px;
        font-weight: 600;
    }
    
    .tab-text {
        font-size: 14px;
    }
    
    .tab-badge {
        font-size: 11px;
        padding: 2px 6px;
    }
    
    .ticket-table {
        font-size: 12px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 8px 12px;
    }
    
    .status-badge,
    .priority-badge {
        font-size: 11px;
        padding: 4px 8px;
    }
    
    .time-progress {
        min-width: 60px;
    }
    
    .time-text {
        font-size: 11px;
    }
    
    .pagination-info {
        font-size: 13px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .page-btn {
        padding: 8px 12px;
        font-size: 13px;
        min-width: 40px;
        height: 40px;
    }
    
    .empty-state {
        padding: 40px 20px;
    }
    
    .empty-state i {
        font-size: 40px;
    }
    
    .empty-state h3 {
        font-size: 18px;
    }
    
    .empty-state p {
        font-size: 14px;
    }
}

@media (max-width: 375px) {
    .page-title {
        font-size: 16px;
    }
    
    .stat-item {
        min-width: 120px;
        padding: 8px 12px;
    }
    
    .toolbar {
        padding: 8px 12px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .search-input {
        padding: 12px 44px 12px 14px;
        font-size: 14px;
    }
    
    .search-btn {
        padding: 6px 10px;
        right: 6px;
    }
    
    .tab-item {
        padding: 12px 14px;
        font-size: 13px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 6px 8px;
        font-size: 12px;
    }
    
    .action-btn {
        padding: 6px 8px;
        font-size: 10px;
    }
} 

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .page-header {
        position: relative;
    }
    
    .main-content {
        padding-top: 12px;
    }
    
    .modal {
        max-height: 95vh;
    }
    
    .modal-body {
        max-height: 60vh;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1400px) {
    .main-content {
        padding: 32px;
    }
    
    .toolbar {
        padding: 20px 24px;
    }
    
    .search-container {
        min-width: 400px;
    }
    
    .form-row {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .ticket-table {
        font-size: 14px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 14px 10px;
    }
}

/* 打印样式 */
@media print {
    .page-header,
    .toolbar,
    .filter-tabs,
    .advanced-search-panel,
    .pagination-container,
    .action-buttons,
    .checkbox-col,
    .actions-col {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        max-width: none;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .ticket-table {
        font-size: 12px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 8px 6px;
        border: 1px solid #ddd;
    }
    
    .ticket-table tbody tr:hover {
        background-color: transparent;
    }
    
    .status-badge,
    .priority-badge {
        border: 1px solid #333;
        color: #333 !important;
        background: transparent !important;
    }
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-outline {
        border-width: 2px;
    }
    
    .status-badge,
    .priority-badge {
        border-width: 2px;
    }
    
    .ticket-table th,
    .ticket-table td {
        border-width: 2px;
    }
    
    .tab-item.active {
        border-bottom-width: 4px;
    }
}

/* 深色模式支持 - 已禁用，确保页面始终使用亮色模式 */
/*
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }

    .page-header,
    .toolbar,
    .filter-tabs,
    .advanced-search-panel,
    .table-container,
    .pagination-container {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .ticket-table th {
        background-color: #404040;
        color: #e0e0e0;
    }

    .ticket-table tbody tr:hover {
        background-color: #404040;
    }

    .btn-outline {
        background-color: #2d2d2d;
        color: #3498db;
        border-color: #3498db;
    }

    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="date"],
    input[type="number"],
    select,
    textarea {
        background-color: #404040;
        border-color: #606060;
        color: #e0e0e0;
    }

    .modal {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }

    .modal-header,
    .modal-footer {
        border-color: #404040;
    }
}
*/
