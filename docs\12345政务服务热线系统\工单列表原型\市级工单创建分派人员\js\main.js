/**
 * 主入口文件
 * 负责应用初始化和全局事件处理
 */

// 全局变量
let ticketListController = null;

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('开始初始化工单列表系统...');
        
        // 检查浏览器兼容性
        if (!checkBrowserCompatibility()) {
            showBrowserCompatibilityWarning();
            return;
        }
        
        // 添加全局样式
        addGlobalStyles();
        
        // 初始化全局错误处理
        initGlobalErrorHandling();
        
        // 初始化工单列表控制器
        ticketListController = new TicketListController();

        // 设置全局变量供其他模块访问
        window.ticketController = ticketListController;

        // 初始化预览面板控制器
        previewController = new PreviewController();

        // 添加页面可见性变化监听
        initPageVisibilityHandler();
        
        // 添加网络状态监听
        initNetworkStatusHandler();
        
        // 添加键盘快捷键
        initKeyboardShortcuts();
        
        console.log('工单列表系统初始化完成');
        
    } catch (error) {
        console.error('系统初始化失败:', error);
        showInitializationError(error);
    }
});

/**
 * 页面卸载时清理
 */
window.addEventListener('beforeunload', function() {
    if (ticketListController) {
        ticketListController.destroy();
    }
});

/**
 * 检查浏览器兼容性
 */
function checkBrowserCompatibility() {
    // 检查必要的API支持
    const requiredFeatures = [
        'fetch',
        'Promise',
        'localStorage',
        'addEventListener',
        'querySelector',
        'classList'
    ];
    
    for (const feature of requiredFeatures) {
        if (!(feature in window) && !(feature in document) && !(feature in Element.prototype)) {
            console.error(`浏览器不支持 ${feature}`);
            return false;
        }
    }
    
    return true;
}

/**
 * 显示浏览器兼容性警告
 */
function showBrowserCompatibilityWarning() {
    const warningHtml = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 99999;
            font-family: Arial, sans-serif;
        ">
            <div style="
                background: #2c3e50;
                padding: 40px;
                border-radius: 8px;
                text-align: center;
                max-width: 500px;
            ">
                <h2 style="margin-bottom: 20px; color: #e74c3c;">
                    <i class="fas fa-exclamation-triangle"></i>
                    浏览器兼容性问题
                </h2>
                <p style="margin-bottom: 20px; line-height: 1.6;">
                    您的浏览器版本过低，可能无法正常使用本系统的所有功能。
                </p>
                <p style="margin-bottom: 30px; line-height: 1.6;">
                    建议升级到以下浏览器的最新版本：
                    <br>Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
                </p>
                <button onclick="location.reload()" style="
                    background: #3498db;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">
                    重新加载
                </button>
            </div>
        </div>
    `;
    
    document.body.innerHTML = warningHtml;
}

/**
 * 添加全局样式
 */
function addGlobalStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* 全局动画 */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
        
        /* 全局工具类 */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        
        .d-none { display: none !important; }
        .d-block { display: block !important; }
        .d-flex { display: flex !important; }
        .d-inline-block { display: inline-block !important; }
        
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-3 { margin-top: 1rem; }
        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 1rem; }
        
        .p-1 { padding: 0.25rem; }
        .p-2 { padding: 0.5rem; }
        .p-3 { padding: 1rem; }
        
        /* 选择高亮 */
        ::selection {
            background-color: rgba(52, 152, 219, 0.2);
        }
        
        /* 焦点样式 */
        .btn:focus,
        input:focus,
        select:focus,
        textarea:focus {
            outline: 2px solid rgba(52, 152, 219, 0.5);
            outline-offset: 2px;
        }
    `;
    
    document.head.appendChild(style);
}

/**
 * 初始化全局错误处理
 */
function initGlobalErrorHandling() {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise错误:', event.reason);
        showErrorMessage('系统发生错误，请刷新页面重试');
        event.preventDefault();
    });
    
    // 捕获JavaScript错误
    window.addEventListener('error', function(event) {
        console.error('JavaScript错误:', event.error);
        showErrorMessage('系统发生错误，请刷新页面重试');
    });
}

/**
 * 初始化页面可见性变化监听
 */
function initPageVisibilityHandler() {
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible' && ticketListController) {
            // 页面重新可见时刷新数据
            console.log('页面重新可见，刷新数据');
            ticketListController.refreshData();
        }
    });
}

/**
 * 初始化网络状态监听
 */
function initNetworkStatusHandler() {
    window.addEventListener('online', function() {
        showSuccessMessage('网络连接已恢复');
        if (ticketListController) {
            ticketListController.refreshData();
        }
    });
    
    window.addEventListener('offline', function() {
        showWarningMessage('网络连接已断开，部分功能可能无法使用');
    });
}

/**
 * 初始化键盘快捷键
 */
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl/Cmd + R: 刷新数据
        if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
            event.preventDefault();
            if (ticketListController) {
                ticketListController.refreshData();
            }
        }
        
        // Ctrl/Cmd + N: 新建工单
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            if (ticketListController) {
                ticketListController.createNewTicket();
            }
        }
        
        // Ctrl/Cmd + F: 聚焦搜索框
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
            event.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // ESC: 关闭模态框和下拉菜单
        if (event.key === 'Escape') {
            // 关闭高级搜索面板
            const advancedPanel = document.getElementById('advancedSearchPanel');
            if (advancedPanel && advancedPanel.style.display !== 'none') {
                ticketListController.toggleAdvancedSearch(false);
            }
            
            // 关闭下拉菜单
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });
}

/**
 * 显示初始化错误
 */
function showInitializationError(error) {
    const errorHtml = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
            z-index: 99999;
        ">
            <h3 style="color: #e74c3c; margin-bottom: 16px;">
                <i class="fas fa-exclamation-circle"></i>
                系统初始化失败
            </h3>
            <p style="margin-bottom: 20px; color: #7f8c8d;">
                ${error.message || '未知错误'}
            </p>
            <button onclick="location.reload()" style="
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
            ">
                重新加载
            </button>
        </div>
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 99998;
        "></div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', errorHtml);
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

/**
 * 显示警告消息
 */
function showWarningMessage(message) {
    showMessage(message, 'warning');
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    showMessage(message, 'error');
}

/**
 * 显示消息
 */
function showMessage(message, type = 'info') {
    if (ticketListController) {
        ticketListController.showMessage(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// 导出全局函数供其他模块使用
window.TicketListApp = {
    showMessage,
    showSuccessMessage,
    showWarningMessage,
    showErrorMessage
};
