
---

### **"12345热线"工单系统完整业务流程**

#### **阶段一：市级统一受理与顶层战略分派**

1.  **受理创建**:
    *   **市民**通过12345热线提出诉求。
    *   **市级工单创建分派人员**接听电话，在系统中**新建工单**，录入所有标准化信息。

2.  **一级分派决策 (市级层面的"条块"战略选择)**:
    *   **市级工单创建分派人员**进行首次、也是最关键的判断：**"此诉求的责任主体是市级直属部门（条），还是应下沉至区级属地管理（块）？"**
    
    *   **场景A：即时办结 (第一效率出口)**
        *   对于"政策咨询"、"信息查询"、"简单告知"等诉求，**市级工单创建分派人员**利用知识库直接答复市民。
        *   在市民表示理解或问题解决后，选择"**即时办结**"，填写办结说明。
        *   工单**跳过所有下派环节**，直接进入**【已关闭】**状态。

    *   **场景B：派发至市级职能部门 (条线处理)**
        *   **适用情况**: 诉求涉及全市范围的宏观政策、跨区域的重大事件、或明确由市级部门垂直管理的事项（如全市性的交通设施问题、市级审批的企业投诉等）。
        *   **操作**: **市级工单创建分派人员**将工单直接派发至对应的**【市级职能部门】**。工单随即进入**市级部门内部**的流转、处理与审核流程。

    *   **场景C：派发至区/县级总口 (块线处理)**
        *   **适用情况**: 绝大部分涉及具体地点、具体民生、需要属地化管理的诉求。
        *   **操作**: **市级工单创建分派人员**根据**地域属性**，将工单派发至对应的**【区/县级12345分中心】**。

    *   **场景D：市级层面的多部门协同**
        *   **适用情况**: 需要市级部门与区级政府协同的重大复杂问题。
        *   **操作**: **市级工单创建分派人员**选择多个承办部门，指定一个**主办部门**（负责总协调和主要责任），其余为**协办部门**（提供专业支持或配合处理）。
        *   工单被一次性派发给主办部门和所有协办部门，启动协同处理流程。

#### **阶段二：工单进入不同轨道的后续流转**

##### **轨道一：市级职能部门内部的流转与处理**

3.  **市级部门接收与分派**:
    *   **各层级工单分派人员（主管）- 市级部门**接收工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则根据职责，将工单下派至**【内部具体的业务处室/科室】**。

4.  **市级科室处理与执行**:
    *   **各层级工单分派人员（主管）- 市级科室**接收工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则指派给**【工单处理人员 - 市级工作人员】**进行处理。

5.  **市级工作人员执行**:
    *   **工单处理人员 - 市级工作人员**执行任务（如调查、出具报告、联系相关企业等），并在系统中**补记**过程。
    *   任务完成后，点击"**办结**"，**进入阶段四**。

##### **轨道二：下沉至区/县后的复杂流转与处理**

3.  **区级总口接收与分派**:
    *   **各层级工单分派人员（主管）- 区级**接收来自市级的工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则进行**区内二次分派 (路径选择)**:
        *   **A. 派发至街/镇 (属地路线)**: 将工单派发至**【街/镇级总承办单位】**。
        *   **B. 派发至区职能部门 (职能路线)**: 将工单派发至**【区级职能部门】**。
        *   **C. 构建区级多部门协同**: 派发给街/镇和区职能部门，指定主办和协办部门。

4.  **区级下沉后的处理与执行**:
    *   **在属地路线**:
        *   **各层级工单分派人员（主管）- 街镇级**接收工单，判断能否**即时办结**。
        *   如不能，则下派至**【街道内设部门】**或**【社区/村委会】**。
        *   **各层级工单分派人员（主管）- 社区级**接收后，判断能否**即时办结**。
        *   如不能，则最终指派给**【工单处理人员 - 网格员】**。
        *   **工单处理人员 - 网格员**执行现场任务，完成后点击"**办结**"，**进入阶段四**。
    *   **在职能路线**:
        *   **各层级工单分派人员（主管）- 区级职能部门**接收工单，判断能否**即时办结**。
        *   如不能，则下派至**【业务科室】**。
        *   **各层级工单分派人员（主管）- 业务科室**接收后，判断能否**即时办结**。
        *   如不能，则最终指派给**【工单处理人员 - 一线工作人员】**。
        *   **工单处理人员 - 一线工作人员**执行任务，完成后点击"**办结**"，**进入阶段四**。

#### **阶段三：工单的逐级下沉与多重办结机会**

5.  **多轨道并行下派与逐级判断**:
    *   工单沿着各自的轨道（无论是市级部门内部，还是区级下沉路线）下派。**每一个接收到工单的层级**，在进行下一步分派前，都**必须先进行"能否即时办结"的判断**。
    *   只有在确认本级无法独立解决时，才会继续**向下派发**。

6.  **最终执行与办结**:
    *   若工单最终下沉至**工单处理人员**（网格员/工作人员），则由其前往现场处理并"**办结**"，触发审核流程。

#### **阶段四：逐级审核反馈与闭环**

7.  **启动审核**: 任何一个层级的"执行办结"或"即时办结"操作完成后，工单都进入**【待审核】**状态。

8.  **逐级审核 (谁派发，谁审核)**:
    *   工单严格按照其"来时"的路径，**自下而上、原路返回**。每一级的管理者都需要对下一级提交的办结结果进行**审核**。审核不通过可**退回**。
    *   **核心原则**: 工单由谁派来，就由谁审核。审核路径是派发路径的精确逆过程。
    *   **审核路径示例1 (属地路线)**:
        *   `工单处理人员-网格员`办结 -> 由`各层级工单分派人员（主管）-社区级`审核 -> `社区`通过 -> 由`各层级工单分派人员（主管）-街镇级`审核 -> `街道`通过 -> 由`各层级工单分派人员（主管）-区级`审核 -> `区级分中心`通过 -> **进入阶段五**。
    *   **审核路径示例2 (职能路线)**:
        *   `工单处理人员-区工作人员`办结 -> 由`各层级工单分派人员（主管）-区业务科室`审核 -> `科室`通过 -> 由`各层级工单分派人员（主管）-区职能部门`审核 -> `部门`通过 -> 由`各层级工单分派人员（主管）-区级`审核 -> `区级分中心`通过 -> **进入阶段五**。
    *   **审核路径示例3 (市级路线)**:
        *   `工单处理人员-市工作人员`办结 -> 由`各层级工单分派人员（主管）-市业务处室`审核 -> `处室`通过 -> 由`各层级工单分派人员（主管）-市职能部门`审核 -> `部门`通过 -> **进入阶段五**。
    *   在任何一级审核中，审核人都可以执行"**退回**"，将工单打回给派发对象，要求返工或补充说明。

9.  **向市级平台反馈**:
    *   无论是从**市级职能部门**办结返回，还是从**区/县12345分中心**办结返回，最终都会向**市级平台**提交"**办结反馈**"。
    *   工单在市级平台的状态统一更新为**【待回访】**。

#### **阶段五：市级统一回访与最终关闭**

10. **独立回访**: **回访员**对所有进入【待回访】状态的工单，统一进行回访。（注：即时办结的工单直接进入【已关闭】状态，跳过回访环节）

11. **最终闭环**:
    *   **市民满意**: **回访员**将工单**【已关闭】**。
    *   **市民不满意**: **回访员**重启工单，附上"**重办督办**"意见，退回至**【市级12345中心】**，启动新一轮的、更高级别的督办处理流程。

---

### **多部门协同流程详解**

#### **市级多部门协同流程**

**启动场景**: 跨区域、跨部门的重大复杂问题，需要多个市级部门和区级政府协同处理。

**示例场景**: 跨多个区域的河流污染问题，涉及环境监测、水务管理、属地处置等多个方面。

1.  **构建协同结构**:
    *   **确定主办部门**: 根据问题性质，选择最相关的市级职能部门作为主办部门，负责总协调和技术鉴定。
    *   **添加协办部门**: 将其他相关的市级部门和涉及区域的12345分中心添加为协办部门。

2.  **多方并行处理**:
    *   所有参与单位的"我的工单"中都会出现此任务，并有明确的"主办"或"协办"标识。
    *   **主办部门**的**各层级工单分派人员（主管）**负责专业调查、技术鉴定和总协调。
    *   **协办部门**的**各层级工单分派人员（主管）**各自承担相应的专业支持或现场配合工作。
    *   所有调查进展通过**工单补记**实时更新，供所有协办部门查看。

3.  **信息汇总与联合处置**:
    *   各方汇总信息，进行联合处置。
    *   协办部门完成任务后，在工单中更新自己的任务状态为"完成"。
    *   **主办部门**的**各层级工单分派人员（主管）**在确认所有问题均已解决、各方均已完成工作后，汇总所有处理情况，形成最终的办结报告，然后点击"**办结**"。

#### **区级多部门协同流程**

**启动场景**: 区级12345分中心接收工单后，判断需要"条块结合"处理。

**示例场景**: 建筑垃圾倾倒问题，需要属地管理和专业执法相结合。

1.  **构建区级协同结构**:
    *   **确定主办部门**: 将**【街道办事处】**设为主办部门，负责现场管理和总协调。
    *   **添加协办部门**: 将需要提供专业执法的区级部门（如区住建局、区生态环境局）添加为协办部门。

2.  **区级协同团队的并行处理**:
    *   **主办部门（街道办事处）**的**各层级工单分派人员（主管）- 街镇级**进行本级再分派，将任务下派给社区和街道城管科，同时与协办部门保持沟通。
    *   **协办部门（区级部门）**的**各层级工单分派人员（主管）**各自进行本级再分派，将任务下派给内部具体的业务科室和**工单处理人员**。
    *   **信息共享**: 所有单位都在同一个工单上进行补记，确保信息实时共享和透明。

#### **街/镇级多部门协同流程**

**启动场景**: 街道办事处接收工单后，判断需要内部多个单位协同作战。

**示例场景**: 流浪犬处理问题，需要城管、卫生、社区等多方协作。

1.  **构建基层协同结构**:
    *   **确定主办部门**: 街道办事处自任主办部门或指定街道综治办作为主办部门。
    *   **添加协办部门**: 将街道城管科、社区卫生服务中心、社区居委会添加为协办部门。

2.  **街道内多方并行处理**:
    *   **协办部门（社区居委会）**的**各层级工单分派人员（主管）- 社区级**首先行动，进行宣传、安抚居民，收集线索。
    *   **协办部门（卫生服务中心）**的**各层级工单分派人员（主管）**制作宣传材料，准备防疫措施。
    *   **协办部门（街道城管科）**的**各层级工单分派人员（主管）**制定捕捉方案，准备专业工具。
    *   **主办部门**的**各层级工单分派人员（主管）- 街镇级**全程跟进各方进度，协调确定最佳执行时间。

#### **多部门协同模式下的审核流程**

多部门协同工单的审核，遵循核心原则：**协办部门对主办部门负责，主办部门对派单方负责。**

1.  **各参与单位内部审核**:
    *   每个参与单位（无论主协办）先在内部完成各自的"执行-审核"小闭环。

2.  **协办部门向主办部门提交确认**:
    *   各协办部门完成内部审核后，在主工单上执行"**提交协办意见**"或"**确认协办完成**"。

3.  **主办部门总办结**:
    *   主办部门在确认自己内部所有任务已完成，并且收到了所有协办部门的"协办完成"确认后，汇总所有信息，形成完整的最终办结报告。
    *   主办部门点击"**办结**"，工单状态变为**【待审核】**。

4.  **派单方最终审核**:
    *   最初的派单方对主办部门提交的、包含了所有协办部门工作成果的完整办结报告进行最终审核。
    *   审核通过后，工单进入**【待回访】**状态。
    *   审核退回时，整个工单退回给主办部门，要求重新协调处理。

---

### **工单状态管理**

#### **状态分类与定义**

**预备状态（非正式工单状态）：**
*   **【草稿】(DRAFT)**: 工单创建过程中的临时保存状态
    - 不分配正式工单编号
    - 仅创建者可编辑、删除
    - 不计入统计报表和考核
    - 不启动SLA计时

**正式业务状态：**
*   **【待接收】(PENDING_RECEIVE)**: 工单已提交派发，等待承办单位确认接收
*   **【处理中】(IN_PROGRESS)**: 承办单位已接收工单并开始处理
*   **【待审核】(PENDING_REVIEW)**: 工单已办结，等待上级审核
*   **【待回访】(PENDING_CALLBACK)**: 审核通过，等待客户回访
*   **【已关闭】(CLOSED)**: 工单处理完成，客户满意，流程结束

**特殊状态：**
*   **【已挂起】(SUSPENDED)**: 因外部条件不具备暂停处理
*   **【已废除】(ABOLISHED)**: 工单被确认为无效或错误

#### **SLA计时规则**
*   **开始计时**: 从【待接收】状态开始
*   **继续计时**: 【处理中】、【待回访】状态
*   **暂停计时**: 【已挂起】、【待审核】状态
*   **停止计时**: 【已关闭】、【已废除】状态
*   **不计时**: 【草稿】状态

---

### **特殊流程处理**

#### **退单流程**

**适用场景**: 当接收方发现工单存在事权不符、信息不完整、重复工单或超出处理能力等情况时。

1.  **退单申请**:
    *   **各层级工单分派人员（主管）**或**工单处理人员**在接收工单后，发现无法处理时，可提交退单申请。
    *   填写退单理由（事权不符/信息不完整/重复工单/超出能力等）。

2.  **退单审核**:
    *   **上级分派者**接收退单申请，审核退单理由。
    *   **合理退单**: 确认退单并重新评估分派，或上报**市级工单主管**处理。
    *   **不合理退单**: 要求下级重新接收处理，并说明理由。

3.  **退单处理**:
    *   对于复杂或有争议的退单，由**市级工单主管**进行最终仲裁。
    *   确保工单得到合理处置，避免推诿扯皮。

#### **延期申请流程**

**适用场景**: 当工单处理时间可能超过规定期限时。

1.  **延期申请**:
    *   **各层级工单分派人员（主管）**或**工单处理人员**可向直接上级申请延期。
    *   说明延期原因和预计完成时间。

2.  **延期审批**:
    *   **直接上级**审核延期申请，决定是否批准。
    *   批准后系统自动调整工单时限。

#### **挂起申请流程**

**适用场景**: 当工单因外部条件不具备无法继续处理时。

1.  **挂起申请**:
    *   **各层级工单分派人员（主管）**或**工单处理人员**可申请挂起工单。
    *   说明挂起原因（等待其他部门配合/政策明确/外部条件等）。

2.  **挂起审批**:
    *   **直接上级**审核挂起申请，决定是否批准。
    *   批准后工单进入挂起状态，暂停计时。

3.  **挂起恢复**:
    *   当挂起条件消除后，可申请恢复工单处理。
    *   恢复后重新开始计时。

#### **工单拆分合并流程**

**工单拆分**: 当发现工单包含多个不同性质问题时。

1.  **发现拆分需求**:
    *   **市级工单创建分派人员**或**各层级工单分派人员（主管）**发现需要拆分。
    *   上报**市级工单主管**处理。

2.  **执行拆分**:
    *   **市级工单主管**将一个工单拆分为多个独立工单。
    *   分别分派给相应的责任部门。

**工单合并**: 当发现多个工单反映同一问题时。

1.  **发现合并需求**:
    *   **市级工单创建分派人员**或**市级工单主管**发现重复工单。

2.  **执行合并**:
    *   **市级工单主管**将相关工单合并为一个工单。
    *   统一处理，避免重复劳动。

---

### **业务流程总结**

本工单系统通过"条块结合"的分派策略和"多部门协同"的处理模式，实现了：

1.  **多层级的即时办结机会**，提高处理效率
2.  **严格的原路返回审核**，确保责任可溯
3.  **灵活的协同处理机制**，应对复杂问题
4.  **统一的回访关闭流程**，保证服务质量
5.  **完善的退单处理机制**，避免推诿扯皮
6.  **灵活的延期挂起机制**，应对特殊情况
7.  **智能的拆分合并功能**，优化工单管理

整个流程既保证了工单处理的规范性和可追溯性，又具备了应对各种复杂情况的灵活性和协同能力，通过完善的特殊流程处理机制，确保每个工单都能得到妥善处理。
