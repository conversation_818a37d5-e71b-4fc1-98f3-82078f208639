
---

### **“12345热线”工单系统完整字段池**

#### **第一类：基础识别信息 (Basic Identification)**

这类字段是工单的唯一标识和核心摘要。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `wo_id` | **工单编号** | 字符串 | 系统生成的唯一ID，例如 `SZ20250728-00001`。是所有查询和关联的基础。 |
| `parent_wo_id` | **父工单编号** | 字符串 | 用于“工单拆分”场景，记录其来源的母工单ID。 |
| `merged_wo_ids` | **被合并工单编号** | 数组 | 用于“工单合并”场景，记录所有被合并进此工单的ID列表。 |
| `title` | **诉求标题** | 字符串 | 对诉求内容的高度概括，由创建人员填写或系统自动提取。在列表中展示。 |
| `content` | **诉求内容** | 长文本 | 市民反映问题的完整、详细描述。 |
| `channel` | **诉求渠道/来源** | 枚举 | 市民通过何种渠道提交。例如：`电话`、`网站`、`APP`、`微信`、`领导信箱`。 |
| `wo_type` | **工单类型** | 枚举 | 对诉求性质的分类。例如：`投诉`、`举报`、`咨询`、`求助`、`建议`、`表扬`。 |
| `biz_category` | **业务分类** | 树形结构 | 多层级的业务领域划分，用于精准派单。例如：`城市管理 -> 市容环境 -> 乱扔垃圾`。 |
| `priority` | **紧急程度/优先级** | 枚举 | 标记工单处理的紧急性。例如：`普通`、`紧急`、`重大`。 |
| `tags` | **关键词/标签** | 数组 | 由创建人员或处理人员添加，便于搜索和归类。例如：`#噪音扰民#` `#重复来电#`。 |

#### **第二类：市民信息 (Citizen Information)**

这类字段记录了服务对象的核心信息。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `citizen_name` | **市民姓名** | 字符串 | 诉求人的称呼，可部分脱敏显示。 |
| `citizen_phone` | **联系电话** | 字符串 | 主要联系方式，用于处理过程中的沟通和最终回访。需加密存储。 |
| `backup_phone` | **备用电话** | 字符串 | 备用联系方式，增强联系成功率。需加密存储。 |
| `citizen_id` | **身份证号** | 字符串 | 用于需要实名验证的场景，高度脱敏显示。 |
| `citizen_type` | **客户类型** | 枚举 | 区分是个人、企业还是其他组织。例如：`个人`、`企业`、`政府机关`、`社会组织`。 |
| `special_identity` | **特殊标识** | 数组 | 标记市民的特殊身份，可能影响处理优先级。例如：`人大代表`、`政协委员`、`残疾人`、`军人`。 |
| `is_anonymous` | **是否匿名** | 布尔值 | 标记市民是否希望匿名。`True` 或 `False`。 |
| `callback_preference` | **回访偏好** | 字符串 | 记录市民希望的回访方式或时间。例如：`短信回访`、`下午5点后方便接听`。 |
| `historical_wo_count` | **历史工单数** | 整数 | 该市民过往的投诉次数，用于识别重复投诉或重点关注对象。系统自动计算。 |
| `historical_satisfaction` | **历史满意度** | 百分比 | 该市民过往的平均满意度，作为服务参考。系统自动计算。 |

#### **第三类：事发位置信息 (Location Information)**

这类字段是“块线”管理和现场处理的基础。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `location_division` | **事发行政区划** | 树形结构 | 结构化的省/市/区/街道数据，用于属地化分派。 |
| `location_address` | **详细地址** | 字符串 | 市民描述的具体地址。 |
| `location_coords` | **地图定位** | 坐标 (Lat, Lng) | 在地图上标记的精确坐标，支持GIS分析和网格员导航。 |
| `location_desc` | **位置描述/参考物** | 字符串 | 对位置的补充说明，例如：“XX超市对面的红色报刊亭旁”。 |

#### **第四类：流程与状态信息 (Process & Status)**

这类字段驱动着工单的生命周期流转。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `current_status` | **当前状态** | 枚举 | 工单所处的生命周期阶段。`草稿`、`待接收`、`处理中`、`待审核`、`待回访`、`已关闭`、`已挂起`、`已废除`。 |
| `processing_mode` | **处理模式** | 枚举 | 标识工单的处理策略。例如：`普通流转`、`即时办结`、`多部门协同`、`紧急处理`。 |
| `creator_id` | **创建人ID** | 字符串 | 创建此工单的人员ID。 |
| `creator_name` | **创建人姓名** | 字符串 | 创建此工单的人员姓名。 |
| `creator_unit` | **创建单位** | 字符串 | 创建人所属的部门/单位，通常是“市级12345中心”。 |
| `create_time` | **创建时间** | 日期时间 | 工单被创建并首次保存的时间。 |
| `submit_time` | **提交时间** | 日期时间 | 工单从【草稿】状态提交，正式进入流转的时间。**SLA开始计时的起点**。 |
| `dispatch_time` | **派单时间** | 日期时间 | 上一级将工单派发出去的时间。 |
| `dispatcher_id` | **派单人ID** | 字符串 | 上一级执行派单操作的人员ID。 |
| `dispatcher_name` | **派单人姓名** | 字符串 | 上一级执行派单操作的人员姓名。 |
| `city_level_unit` | **市级处理单位** | 字符串 | 市级派发的目标单位（如"XX区政府"）。市级及以上可见。 |
| `district_level_unit` | **区级处理单位** | 字符串 | 区级派发的目标单位（如"XX街道办"）。区级及以上可见，市级不可见。 |
| `street_level_unit` | **街镇级处理单位** | 字符串 | 街镇派发的目标单位（如"XX社区"）。街镇级及以上可见，区级以上不可见。 |
| `community_level_unit` | **社区级处理单位** | 字符串 | 社区级处理单位。仅社区级及街镇级可见。 |
| `current_processing_level` | **当前处理层级** | 枚举 | 工单当前所在的行政层级。例如：`市级`、`区级`、`街镇级`、`社区级`。 |
| `responsible_unit` | **责任单位** | 字符串 | 当前负责处理此工单的单位名称。系统根据用户层级自动显示对应层级的单位。 |
| `handler_role` | **处理角色** | 枚举 | 当前处理环节的角色类型。例如：`工单分派人员`、`工单处理人员`、`审核人员`、`回访员`。 |
| `internal_handler_id` | **内部处理人ID** | 字符串 | 内部管理用的处理人ID，仅限本级及下级系统使用。 |
| `receive_time` | **接收时间** | 日期时间 | 当前单位接收工单的时间。 |
| `completion_time`| **办结时间** | 日期时间 | 一线处理人员点击“办结”的时间，工单进入【待审核】。 |
| `completion_method` | **办结方式** | 枚举 | 记录工单是如何被办结的。例如：`现场办结`、`协调办结`、`即时办结`、`转办办结`。 |
| `completion_unit` | **办结单位** | 字符串 | 最终执行办结操作的单位。 |
| `internal_completer_id` | **内部办结人ID** | 字符串 | 内部管理用的办结人ID，仅限内部系统使用。 |
| `review_time` | **审核时间** | 日期时间 | 最后一级审核通过的时间，工单进入【待回访】。 |
| `close_time` | **关闭时间** | 日期时间 | 回访员点击“关闭”的时间，工单生命周期结束。**SLA停止计时的终点**。 |
| `close_type` | **关闭类型** | 枚举 | 记录关闭方式。例如：`回访满意关闭`、`即时办结关闭`、`无需回访关闭`。 |
| `close_unit` | **关闭单位** | 字符串 | 执行关闭操作的单位（通常是市级12345中心）。 |
| `internal_closer_id` | **内部关闭人ID** | 字符串 | 内部管理用的关闭人ID，仅限内部系统使用。 |
| `last_update_time`| **最近更新时间** | 日期时间 | 工单任何信息发生变更的最新时间。 |

#### **第五类：权责与流转信息 (Responsibility & Flow)**

这类字段记录了工单在不同部门和人员之间的流转轨迹。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `primary_unit` | **主办单位** | 字符串 | 在协同模式下，负主要责任的单位。 |
| `collaborator_units` | **协办单位列表** | 数组对象 | 协同模式下的参与单位列表，每个对象包含 `{unit_id, unit_name, status}`。 |
| `cc_units` | **抄送单位列表** | 数组 | 需要知晓此工单但不参与处理的单位列表（如各层级领导）。 |
| `flow_history` | **流转历史记录** | 数组对象 | 核心审计字段，记录每一次流转。每个对象包含 `{时间, 操作类型, 操作人, 源单位, 目标单位, 层级, 备注}`。 |
| `flow_history_filtered` | **流转历史** | 数组对象 | 工单流转历史记录，系统根据用户权限自动过滤显示范围。 |

#### **第六类：处置与反馈信息 (Disposition & Feedback)**

这类字段记录了问题的处理过程、结果和最终评价。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `processing_log` | **处理过程记录** | 数组对象 | 即“工单补记”，处理人员可多次追加。每个对象包含 `{时间, 记录人, 内容, 附件}`。 |
| `completion_report` | **办结报告/结果说明**| 长文本 | 处理人员在点击“办结”时填写的最终处理结果总结。 |
| `review_status` | **审核状态** | 枚举 | 标识审核流程的状态。例如：`待审核`、`审核通过`、`审核退回`。 |
| `review_comments` | **审核意见** | 数组对象 | 各级审核人员的意见记录。每个对象包含 `{时间, 审核人, 审核结果, 意见内容}`。 |
| `review_unit` | **审核单位** | 字符串 | 执行审核操作的单位。 |
| `internal_reviewer_id` | **内部审核人ID** | 字符串 | 内部管理用的审核人ID，仅限内部系统使用。 |
| `review_history` | **审核历史** | 数组对象 | 记录所有层级的审核与退回历史。 |
| `return_reason` | **退回原因** | 长文本 | 审核不通过时，必须填写的具体原因。 |
| `return_count` | **退回次数** | 整数 | 记录工单被退回的总次数。 |
| `callback_status` | **回访状态** | 枚举 | 标记回访工作的进度。例如：`待回访`、`回访中`、`已回访`、`无需回访`。 |
| `callback_unit` | **回访单位** | 字符串 | 负责执行回访的单位（通常是市级12345中心）。 |
| `internal_callback_staff_id` | **内部回访员ID** | 字符串 | 内部管理用的回访员ID，仅限内部系统使用。 |
| `callback_method` | **回访方式** | 枚举 | 记录回访的渠道。例如：`电话回访`、`短信回访`、`上门回访`。 |
| `callback_log` | **回访记录** | 长文本 | 回访员与市民的沟通记录。 |
| `satisfaction_level` | **市民满意度** | 枚举 | 回访结果。例如：`非常满意`、`满意`、`基本满意`、`不满意`。 |
| `dissatisfaction_reason`| **不满意原因** | 长文本 | 当市民不满意时，记录的具体原因。 |
| `dissatisfaction_category`| **不满意原因分类** | 数组 | 如果不满意，选择具体的原因分类。例如：`处理不及时`、`态度不好`、`结果不满意`。 |
| `reopen_count` | **重启次数** | 整数 | 记录因不满意而被重启的次数，是重要的质量指标。 |
| `reopen_history` | **重启历史** | 数组对象 | 记录每次重启的原因和时间。 |

#### **第七类：附件与关联资源 (Attachments & Resources)**

这类字段存储与工单相关的多媒体文件和参考资料。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `attachments` | **附件列表** | 数组对象 | 上传的所有文件。每个对象包含 `{文件名, 文件类型, 大小, 上传人, 上传时间, 存储路径}`。 |
| `linked_kb_articles`| **关联知识库文章** | 数组 | 关联的知识库文章ID，用于辅助处理和统计分析。 |

#### **第八类：SLA与时效信息 (SLA & Timing)**

这类字段用于监控和考核工单处理时效。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `sla_policy` | **SLA策略** | 字符串 | 应用于此工单的SLA策略名称。 |
| `reception_deadline` | **接收时限** | 日期时间 | 工单必须被接收的截止时间。 |
| `processing_deadline` | **处理时限** | 日期时间 | 工单必须被关闭的最终截止时间。 |
| `sla_status` | **当前SLA状态** | 枚举 | `正常`、`即将超时` (预警)、`已超时` (违规)。 |
| `suspension_history`| **挂起历史** | 数组对象 | 记录每次挂起和解挂的操作。 |
| `extension_history` | **延期历史** | 数组对象 | 记录每次申请延期并获批的操作。 |
| `remaining_time_current` | **当前环节剩余时间** | 字符串 | 动态计算当前步骤的剩余处理时间，是预警的核心。例如：`2天5小时`、`已超时`。 |
| `remaining_time_total` | **总体处理剩余时间** | 字符串 | 动态计算整个工单的剩余处理时间。 |
| `timeout_status` | **超时状态** | 枚举 | 标记工单是否超时。例如：`正常`、`即将超时`、`已超时`。 |

#### **第九类：系统与元数据 (System & Metadata)**

这类字段由系统在后台使用，用于控制逻辑和特殊标记。

| 字段ID | 字段名称 | 数据类型 | 描述与说明 |
| :--- | :--- | :--- | :--- |
| `is_public_opinion` | **是否舆情工单** | 布尔值 | 特殊标记，用于触发不同的关注和处理流程。 |
| `is_duplicate` | **是否重复工单** | 布尔值 | 由系统或人工标记的重复工单。 |
| `requires_callback` | **是否需要回访** | 布尔值 | 控制工单是否进入回访环节。例如“即时办结”的工单此项为`False`。 |
| `is_reopened` | **是否重开工单** | 布尔值 | 标记此工单是否由“不满意”重启而来。 |
| `supervision_level` | **督办级别** | 枚举 | 标记工单的督办等级。例如：`无`、`区级督办`、`市级督办`、`省级督办`。 |
| `supervision_record` | **督办记录** | 数组对象 | 记录所有督办操作的时间、督办人和督办意见。 |
| `urge_count` | **催办次数** | 整数 | 记录工单被催办的次数。 |
| `suspension_status` | **挂起状态** | 布尔值 | 标记工单是否处于暂停处理的状态。 |
| `suspension_reason` | **挂起原因** | 长文本 | 申请暂停处理的原因（如等待市民补充材料）。 |
| `suspension_history` | **挂起/解挂历史** | 数组对象 | 记录所有挂起和恢复处理的操作。 |
| `relation_type` | **关联类型** | 枚举 | 标记工单的关联关系。例如：`主工单`、`子工单`、`被合并工单`。 |
| `related_wo_ids` | **关联工单编号** | 数组 | 指向与本工单关联的其他工单的编号。 |

---

### **字段池使用说明**

#### **字段分类统计**
- **第一类：基础识别信息** - 10个字段
- **第二类：市民信息** - 10个字段
- **第三类：事发位置信息** - 4个字段
- **第四类：流程与状态信息** - 24个字段
- **第五类：权责与流转信息** - 4个字段
- **第六类：处置与反馈信息** - 20个字段
- **第七类：附件与关联资源** - 2个字段
- **第八类：SLA与时效信息** - 11个字段
- **第九类：系统与元数据** - 10个字段

**总计：95个字段**

#### **核心字段标识**
以下字段为系统核心字段，在所有工单列表视图中建议优先显示：
- `wo_id` (工单编号)
- `title` (诉求标题)
- `wo_type` (工单类型)
- `current_status` (当前状态)
- `priority` (紧急程度)
- `citizen_name` (市民姓名)
- `citizen_phone` (联系电话)
- `responsible_unit` (责任单位)
- `create_time` (创建时间)
- `remaining_time_total` (总体处理剩余时间)
- `timeout_status` (超时状态)

#### **字段使用原则**
1. **必填字段**：基础识别信息和市民信息中的核心字段
2. **条件字段**：根据工单类型和处理模式动态显示
3. **系统字段**：由系统自动计算和维护，用户不可直接编辑
4. **脱敏字段**：涉及个人隐私的字段需要根据用户权限进行脱敏显示
5. **审计字段**：所有历史记录类字段用于审计追溯，不可删除

#### **层级信息隔离设计**

**核心原则：**
1. **分级管理** - 每一级只管理和监督直接下一级
2. **信息隔离** - 上级看不到下级的内部流转详情
3. **权限分层** - 不同行政层级看到不同范围的信息
4. **单位责任制** - 强调部门/单位责任，弱化个人责任

**层级信息隔离规则：**

| 用户层级 | 可见处理单位字段 | 可见流转范围 | 隔离说明 |
|:---|:---|:---|:---|
| **市级用户** | `city_level_unit` | 市级 → 区级 | 只能看到派给了哪个区，看不到区内部流转 |
| **区级用户** | `city_level_unit` + `district_level_unit` | 市级 → 区级 → 街镇级 | 能看到区内派发，但看不到街镇内部流转 |
| **街镇级用户** | 前三级字段 | 市级 → 区级 → 街镇级 → 社区级 | 能看到派给了哪个社区，但看不到社区内部具体人员 |
| **社区级用户** | 所有层级字段 | 完整流转路径 | 能看到社区内部的具体处理人员 |

**信息隔离场景示例：**
```
市级派单: "XX市政府" → "XX区政府"
         ↓ (市级只能看到这一层)
区级派单: "XX区政府" → "XX街道办"
         ↓ (区级能看到这一层，市级看不到)
街镇派单: "XX街道办" → "XX社区"
         ↓ (街镇能看到这一层，区级以上看不到)
社区派单: "XX社区" → "具体工作人员"
         ↓ (只有社区能看到具体是谁，街镇只知道派给了XX社区)
```

**系统自动处理：**
- `responsible_unit` - 系统根据用户层级自动显示对应的责任单位
- `flow_history_filtered` - 系统根据用户权限自动过滤流转历史
- `current_processing_level` - 标识工单当前所在的行政层级

#### **扩展说明**
本字段池设计支持：
- 多种工单类型的差异化字段需求
- 复杂的多部门协同处理流程
- 完整的SLA时效管理
- 全流程的审计追溯
- 严格的信息隔离和权限控制
- 人员弱化的责任体系
- 丰富的统计分析维度