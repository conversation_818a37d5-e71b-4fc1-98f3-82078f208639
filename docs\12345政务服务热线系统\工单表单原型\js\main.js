/**
 * 主入口文件
 * 应用程序的初始化和全局事件处理
 */

class TicketFormApp {
    constructor() {
        this.isInitialized = false;
        this.components = {
            formValidator: null,
            uiComponents: null,
            formHandler: null
        };
        
        this.init();
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initApp());
            } else {
                await this.initApp();
            }
        } catch (error) {
            console.error('应用程序初始化失败:', error);
            this.showErrorMessage('应用程序初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化应用程序核心功能
     */
    async initApp() {
        console.log('正在初始化12345热线工单表单系统...');
        
        // 显示加载状态
        this.showLoadingState();
        
        try {
            // 1. 初始化全局组件
            await this.initGlobalComponents();
            
            // 2. 初始化表单数据
            await this.initFormData();
            
            // 3. 初始化事件监听
            this.initEventListeners();
            
            // 4. 初始化键盘快捷键
            this.initKeyboardShortcuts();
            
            // 5. 初始化性能监控
            this.initPerformanceMonitoring();
            
            // 6. 标记为已初始化
            this.isInitialized = true;
            
            console.log('12345热线工单表单系统初始化完成');
            
            // 隐藏加载状态
            this.hideLoadingState();
            
            // 显示欢迎消息
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('应用程序初始化过程中出错:', error);
            this.showErrorMessage('系统初始化失败，请刷新页面重试');
            this.hideLoadingState();
        }
    }

    /**
     * 初始化全局组件
     */
    async initGlobalComponents() {
        console.log('初始化全局组件...');
        
        // 验证全局对象是否存在
        if (typeof formValidator === 'undefined') {
            throw new Error('表单验证器未加载');
        }
        
        if (typeof uiComponents === 'undefined') {
            throw new Error('UI组件未加载');
        }
        
        if (typeof formHandler === 'undefined') {
            throw new Error('表单处理器未加载');
        }
        
        // 保存组件引用
        this.components.formValidator = formValidator;
        this.components.uiComponents = uiComponents;
        this.components.formHandler = formHandler;
        
        console.log('全局组件初始化完成');
    }

    /**
     * 初始化表单数据
     */
    async initFormData() {
        console.log('初始化表单数据...');
        
        try {
            // 生成工单编号
            const ticketNumber = UTILS.generateTicketNumber();
            document.getElementById('ticketNumber').value = ticketNumber;
            
            // 设置创建时间
            const createTime = UTILS.formatDateTime();
            document.getElementById('createTime').value = createTime;
            
            // 加载用户偏好设置
            const preferences = UTILS.storage.get(CONSTANTS.STORAGE_KEYS.USER_PREFERENCES, MOCK_USER_PREFERENCES);
            this.applyUserPreferences(preferences);
            
            // 加载历史地址
            const recentAddresses = UTILS.storage.get(CONSTANTS.STORAGE_KEYS.RECENT_ADDRESSES, MOCK_RECENT_ADDRESSES);
            this.loadRecentAddresses(recentAddresses);
            
            // 加载自定义标签历史
            const customTags = UTILS.storage.get(CONSTANTS.STORAGE_KEYS.CUSTOM_TAGS, MOCK_CUSTOM_TAGS);
            this.loadCustomTagHistory(customTags);
            
            console.log('表单数据初始化完成');
            
        } catch (error) {
            console.error('表单数据初始化失败:', error);
            throw error;
        }
    }

    /**
     * 应用用户偏好设置
     */
    applyUserPreferences(preferences) {
        // 设置默认工单来源
        if (preferences.defaultTicketSource) {
            const sourceSelect = document.getElementById('ticketSource');
            if (sourceSelect) {
                sourceSelect.value = preferences.defaultTicketSource;
            }
        }
        
        // 设置默认紧急程度
        if (preferences.defaultPriority) {
            const priorityRadio = document.querySelector(`input[name="priority"][value="${preferences.defaultPriority}"]`);
            if (priorityRadio) {
                priorityRadio.checked = true;
            }
        }
        
        // 设置默认处理模式
        if (preferences.defaultProcessMode) {
            const processModeRadio = document.querySelector(`input[name="processMode"][value="${preferences.defaultProcessMode}"]`);
            if (processModeRadio) {
                processModeRadio.checked = true;
                // 触发change事件以更新UI
                processModeRadio.dispatchEvent(new Event('change'));
            }
        }
    }

    /**
     * 加载最近使用的地址
     */
    loadRecentAddresses(addresses) {
        // 这里可以将最近地址添加到地址建议中
        // 实际实现中可能需要更复杂的逻辑
        console.log('加载最近地址:', addresses.length, '条');
    }

    /**
     * 加载自定义标签历史
     */
    loadCustomTagHistory(tags) {
        // 这里可以将历史标签添加到建议中
        console.log('加载自定义标签历史:', tags.length, '个');
    }

    /**
     * 初始化事件监听
     */
    initEventListeners() {
        console.log('初始化事件监听...');
        
        // 全局错误处理
        window.addEventListener('error', (e) => {
            console.error('全局错误:', e.error);
            this.handleGlobalError(e.error);
        });
        
        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            console.error('未处理的Promise拒绝:', e.reason);
            this.handleGlobalError(e.reason);
        });
        
        // 网络状态变化
        window.addEventListener('online', () => {
            this.handleNetworkStatusChange(true);
        });
        
        window.addEventListener('offline', () => {
            this.handleNetworkStatusChange(false);
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 窗口大小变化
        window.addEventListener('resize', UTILS.throttle(() => {
            this.handleWindowResize();
        }, 250));
        
        console.log('事件监听初始化完成');
    }

    /**
     * 初始化键盘快捷键
     */
    initKeyboardShortcuts() {
        console.log('初始化键盘快捷键...');
        
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存草稿
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.components.formHandler.saveDraft();
                return;
            }
            
            // Ctrl+Enter 提交表单
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.components.formHandler.submitTicket();
                return;
            }
            
            // Ctrl+R 重置表单
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                document.getElementById('resetForm').click();
                return;
            }
            
            // F1 显示帮助
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelp();
                return;
            }
            
            // Escape 关闭模态框
            if (e.key === 'Escape') {
                const visibleModal = document.querySelector('.modal.show');
                if (visibleModal) {
                    this.components.uiComponents.hideModal(visibleModal);
                }
                return;
            }
        });
        
        console.log('键盘快捷键初始化完成');
    }

    /**
     * 初始化性能监控
     */
    initPerformanceMonitoring() {
        console.log('初始化性能监控...');
        
        // 监控页面加载性能
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData) {
                        console.log('页面加载性能:', {
                            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                            totalTime: perfData.loadEventEnd - perfData.fetchStart
                        });
                    }
                }, 0);
            });
        }
        
        // 监控内存使用（如果支持）
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('内存使用率过高:', {
                        used: memory.usedJSHeapSize,
                        total: memory.totalJSHeapSize,
                        limit: memory.jsHeapSizeLimit
                    });
                }
            }, 30000); // 每30秒检查一次
        }
        
        console.log('性能监控初始化完成');
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(error) {
        // 记录错误
        console.error('全局错误处理:', error);
        
        // 显示用户友好的错误消息
        if (this.isInitialized) {
            this.components.uiComponents.showMessage('系统出现异常，请刷新页面重试', 'error');
        }
        
        // 可以在这里添加错误上报逻辑
        // this.reportError(error);
    }

    /**
     * 处理网络状态变化
     */
    handleNetworkStatusChange(isOnline) {
        console.log('网络状态变化:', isOnline ? '在线' : '离线');
        
        if (this.isInitialized) {
            if (isOnline) {
                this.components.uiComponents.showMessage('网络连接已恢复', 'success');
                // 可以在这里同步离线数据
            } else {
                this.components.uiComponents.showMessage('网络连接已断开，数据将保存在本地', 'warning');
            }
        }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('页面变为不可见');
            // 页面不可见时可以暂停一些操作
        } else {
            console.log('页面变为可见');
            // 页面可见时可以恢复操作
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        console.log('窗口大小变化:', window.innerWidth, 'x', window.innerHeight);
        
        // 可以在这里处理响应式布局调整
        // 例如：调整模态框大小、重新计算布局等
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'appLoadingOverlay';
        loadingOverlay.className = 'loading-overlay show';
        loadingOverlay.innerHTML = `
            <div class="loading-spinner"></div>
            <div class="loading-message">正在初始化系统...</div>
        `;
        document.body.appendChild(loadingOverlay);
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        const loadingOverlay = document.getElementById('appLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
            setTimeout(() => {
                loadingOverlay.remove();
            }, 300);
        }
    }

    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        // 检查是否是首次访问
        const isFirstVisit = !UTILS.storage.get('hasVisited');

        if (isFirstVisit) {
            UTILS.storage.set('hasVisited', true);
            setTimeout(() => {
                this.components.uiComponents.showMessage('欢迎使用12345热线工单表单系统！', 'info');
                // 显示时限设置提示
                setTimeout(() => {
                    this.components.uiComponents.showMessage('时限已设置为默认值，选择工单类型后会自动调整', 'info');
                }, 2000);
            }, 500);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <span class="icon">❌</span>
            <span>${message}</span>
        `;
        
        document.body.insertBefore(errorDiv, document.body.firstChild);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        const helpContent = `
            <div class="help-content">
                <h3>键盘快捷键</h3>
                <ul>
                    <li><kbd>Ctrl + S</kbd> - 保存草稿</li>
                    <li><kbd>Ctrl + Enter</kbd> - 提交工单</li>
                    <li><kbd>Ctrl + R</kbd> - 重置表单</li>
                    <li><kbd>F1</kbd> - 显示帮助</li>
                    <li><kbd>Esc</kbd> - 关闭模态框</li>
                </ul>
                
                <h3>使用说明</h3>
                <ul>
                    <li>表单会自动保存草稿，无需担心数据丢失</li>
                    <li>必填字段标有红色星号(*)标识</li>
                    <li>支持拖拽上传文件，也可以粘贴图片</li>
                    <li>地址输入支持智能提示</li>
                    <li>可以添加业务标签和自定义标签</li>
                </ul>
                
                <h3>技术支持</h3>
                <p>如遇问题请联系技术支持：400-123-4567</p>
            </div>
        `;
        
        // 创建帮助模态框
        const helpModal = document.createElement('div');
        helpModal.className = 'modal';
        helpModal.dataset.dynamic = 'true';
        helpModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>使用帮助</h3>
                    <button type="button" class="btn-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${helpContent}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-close">关闭</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(helpModal);
        this.components.uiComponents.showModal(helpModal);
    }

    /**
     * 获取应用程序状态
     */
    getAppStatus() {
        return {
            initialized: this.isInitialized,
            components: Object.keys(this.components).reduce((status, key) => {
                status[key] = this.components[key] !== null;
                return status;
            }, {}),
            performance: {
                memory: 'memory' in performance ? performance.memory : null,
                timing: performance.timing
            }
        };
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        console.log('销毁应用程序...');
        
        // 销毁组件
        if (this.components.formHandler && typeof this.components.formHandler.destroy === 'function') {
            this.components.formHandler.destroy();
        }
        
        // 清理事件监听
        // 注意：这里只是示例，实际实现中需要保存事件监听器的引用以便清理
        
        // 清理定时器
        // 清理其他资源
        
        this.isInitialized = false;
        console.log('应用程序已销毁');
    }
}

// 创建全局应用程序实例
const ticketFormApp = new TicketFormApp();

// 将应用程序实例暴露到全局作用域（用于调试）
if (typeof window !== 'undefined') {
    window.ticketFormApp = ticketFormApp;
}

// 导出模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TicketFormApp, ticketFormApp };
}

// 应用程序加载完成后的回调
console.log('12345热线工单表单系统脚本加载完成');
