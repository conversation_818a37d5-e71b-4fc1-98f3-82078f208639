/**
 * 系统配置文件
 * 包含系统配置、常量定义、枚举值等
 */

// 系统配置
const CONFIG = {
    // 分页配置
    pagination: {
        defaultPageSize: 50,
        pageSizeOptions: [20, 50, 100],
        maxPageSize: 100
    },
    
    // 表格配置
    table: {
        defaultSortField: 'createTime',
        defaultSortOrder: 'desc',
        maxSelectCount: 1000,
        autoRefreshInterval: 30000 // 30秒自动刷新
    },
    
    // 筛选配置
    filter: {
        maxHistoryCount: 10,
        debounceDelay: 300,
        cacheExpiry: 300000 // 5分钟缓存过期
    },
    
    // API配置（模拟）
    api: {
        baseUrl: '/api/v1',
        timeout: 30000,
        retryTimes: 3,
        endpoints: {
            getTickets: '/tickets',
            createTicket: '/tickets',
            updateTicket: '/tickets/{id}',
            deleteTicket: '/tickets/{id}',
            assignTicket: '/tickets/{id}/assign',
            closeTicket: '/tickets/{id}/close',
            exportTickets: '/tickets/export'
        }
    },
    
    // UI配置
    ui: {
        animationDuration: 300,
        toastDuration: 3000,
        loadingDelay: 500
    }
};

// 工单状态枚举
const TICKET_STATUS = {
    DRAFT: 'draft',
    PENDING: 'pending',
    PROCESSING: 'processing',
    CLOSED: 'closed',
    RETURNED: 'returned'
};

// 工单状态显示配置
const STATUS_CONFIG = {
    [TICKET_STATUS.DRAFT]: {
        label: '草稿',
        class: 'draft',
        icon: 'fas fa-edit',
        color: '#6c757d'
    },
    [TICKET_STATUS.PENDING]: {
        label: '待接收',
        class: 'pending',
        icon: 'fas fa-clock',
        color: '#f39c12'
    },
    [TICKET_STATUS.PROCESSING]: {
        label: '处理中',
        class: 'processing',
        icon: 'fas fa-cog',
        color: '#3498db'
    },
    [TICKET_STATUS.CLOSED]: {
        label: '已关闭',
        class: 'closed',
        icon: 'fas fa-check-circle',
        color: '#27ae60'
    },
    [TICKET_STATUS.RETURNED]: {
        label: '已退回',
        class: 'returned',
        icon: 'fas fa-undo',
        color: '#e74c3c'
    }
};

// 优先级枚举
const PRIORITY_LEVEL = {
    URGENT: 'urgent',
    HIGH: 'high',
    NORMAL: 'normal',
    LOW: 'low'
};

// 优先级显示配置
const PRIORITY_CONFIG = {
    [PRIORITY_LEVEL.URGENT]: {
        label: '紧急',
        class: 'urgent',
        icon: 'fas fa-exclamation-triangle',
        color: '#e74c3c',
        weight: 4
    },
    [PRIORITY_LEVEL.HIGH]: {
        label: '重要',
        class: 'high',
        icon: 'fas fa-exclamation-circle',
        color: '#f39c12',
        weight: 3
    },
    [PRIORITY_LEVEL.NORMAL]: {
        label: '一般',
        class: 'normal',
        icon: 'fas fa-circle',
        color: '#3498db',
        weight: 2
    },
    [PRIORITY_LEVEL.LOW]: {
        label: '较低',
        class: 'low',
        icon: 'fas fa-circle',
        color: '#95a5a6',
        weight: 1
    }
};

// 工单类型枚举
const TICKET_TYPE = {
    COMPLAINT: 'complaint',
    CONSULTATION: 'consultation',
    SUGGESTION: 'suggestion',
    HELP: 'help'
};

// 工单类型显示配置
const TYPE_CONFIG = {
    [TICKET_TYPE.COMPLAINT]: {
        label: '投诉',
        class: 'complaint',
        icon: 'fas fa-exclamation-triangle',
        color: '#e74c3c'
    },
    [TICKET_TYPE.CONSULTATION]: {
        label: '咨询',
        class: 'consultation',
        icon: 'fas fa-question-circle',
        color: '#3498db'
    },
    [TICKET_TYPE.SUGGESTION]: {
        label: '建议',
        class: 'suggestion',
        icon: 'fas fa-lightbulb',
        color: '#f39c12'
    },
    [TICKET_TYPE.HELP]: {
        label: '求助',
        class: 'help',
        icon: 'fas fa-hands-helping',
        color: '#27ae60'
    }
};

// 诉求渠道枚举
const CHANNEL_TYPE = {
    PHONE: 'phone',
    WEBSITE: 'website',
    APP: 'app',
    WECHAT: 'wechat',
    EMAIL: 'email',
    LETTER: 'letter'
};

// 诉求渠道显示配置
const CHANNEL_CONFIG = {
    [CHANNEL_TYPE.PHONE]: {
        label: '电话',
        icon: 'fas fa-phone',
        color: '#3498db'
    },
    [CHANNEL_TYPE.WEBSITE]: {
        label: '网站',
        icon: 'fas fa-globe',
        color: '#27ae60'
    },
    [CHANNEL_TYPE.APP]: {
        label: 'APP',
        icon: 'fas fa-mobile-alt',
        color: '#9b59b6'
    },
    [CHANNEL_TYPE.WECHAT]: {
        label: '微信',
        icon: 'fab fa-weixin',
        color: '#2ecc71'
    },
    [CHANNEL_TYPE.EMAIL]: {
        label: '邮件',
        icon: 'fas fa-envelope',
        color: '#f39c12'
    },
    [CHANNEL_TYPE.LETTER]: {
        label: '信件',
        icon: 'fas fa-mail-bulk',
        color: '#95a5a6'
    }
};

// 责任单位配置
const RESPONSIBLE_UNITS = {
    'urban': '城管局',
    'transport': '交通局',
    'housing': '住建局',
    'education': '教育局',
    'health': '卫健委',
    'environment': '生态环境局',
    'water': '水务局',
    'emergency': '应急管理局',
    'market': '市场监管局',
    'police': '公安局'
};

// 筛选器配置
const FILTER_CONFIG = {
    myDrafts: {
        label: '我的草稿',
        condition: (ticket, currentUser) => 
            ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === currentUser.id
    },
    returned: {
        label: '被退回件',
        condition: (ticket, currentUser) => 
            ticket.status === TICKET_STATUS.DRAFT && ticket.returnCount > 0 && ticket.creatorId === currentUser.id
    },
    assigned: {
        label: '已派待接',
        condition: (ticket) => 
            ticket.status === TICKET_STATUS.PENDING
    },
    closed: {
        label: '我已办结',
        condition: (ticket, currentUser) => 
            ticket.status === TICKET_STATUS.CLOSED && ticket.closeType === 'immediate' && ticket.creatorId === currentUser.id
    },
    today: {
        label: '今日新增',
        condition: (ticket) => {
            const today = new Date().toDateString();
            return new Date(ticket.createTime).toDateString() === today;
        }
    },
    urgent: {
        label: '紧急/重大',
        condition: (ticket) => 
            ticket.priority === PRIORITY_LEVEL.URGENT || ticket.priority === PRIORITY_LEVEL.HIGH
    },
    all: {
        label: '全部工单',
        condition: () => true
    }
};

// 操作权限配置
const PERMISSION_CONFIG = {
    // 页面级操作权限
    pageActions: {
        createTicket: true,
        batchAssign: true,
        batchClose: true,
        export: true,
        import: false
    },
    
    // 行级操作权限
    rowActions: {
        view: true,
        edit: (ticket, user) => ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === user.id,
        assign: (ticket, user) => ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === user.id,
        close: (ticket, user) => ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === user.id,
        recall: (ticket, user) => ticket.status === TICKET_STATUS.PENDING && ticket.creatorId === user.id,
        urge: (ticket, user) => ticket.status === TICKET_STATUS.PENDING,
        copy: true,
        delete: (ticket, user) => ticket.status === TICKET_STATUS.DRAFT && ticket.creatorId === user.id
    }
};

// 表格列配置
const TABLE_COLUMNS = [
    {
        key: 'woId',
        label: '工单编号',
        sortable: true,
        width: '120px',
        fixed: true
    },
    {
        key: 'title',
        label: '诉求标题',
        sortable: true,
        width: '200px',
        ellipsis: true
    },
    {
        key: 'status',
        label: '当前状态',
        sortable: true,
        width: '100px',
        render: 'status'
    },
    {
        key: 'priority',
        label: '紧急程度',
        sortable: true,
        width: '100px',
        render: 'priority'
    },
    {
        key: 'channel',
        label: '诉求渠道',
        sortable: true,
        width: '100px',
        render: 'channel'
    },
    {
        key: 'type',
        label: '工单类型',
        sortable: true,
        width: '100px',
        render: 'type'
    },
    {
        key: 'citizenName',
        label: '市民姓名',
        sortable: true,
        width: '100px',
        privacy: true
    },
    {
        key: 'creatorName',
        label: '创建人',
        sortable: true,
        width: '100px'
    },
    {
        key: 'createTime',
        label: '创建时间',
        sortable: true,
        width: '140px',
        render: 'datetime'
    },
    {
        key: 'responsibleUnit',
        label: '责任单位',
        sortable: true,
        width: '120px',
        render: 'unit'
    },
    {
        key: 'remainingTime',
        label: '剩余时间',
        sortable: true,
        width: '120px',
        render: 'timeProgress'
    }
];

// 导出配置
const EXPORT_CONFIG = {
    formats: ['excel', 'csv'],
    maxRecords: 10000,
    filename: '工单列表_{date}',
    columns: TABLE_COLUMNS.filter(col => col.key !== 'actions')
};

// 消息提示配置
const MESSAGE_CONFIG = {
    success: {
        icon: 'fas fa-check-circle',
        color: '#27ae60',
        duration: 3000
    },
    warning: {
        icon: 'fas fa-exclamation-triangle',
        color: '#f39c12',
        duration: 4000
    },
    error: {
        icon: 'fas fa-times-circle',
        color: '#e74c3c',
        duration: 5000
    },
    info: {
        icon: 'fas fa-info-circle',
        color: '#3498db',
        duration: 3000
    }
};

// 当前用户信息（模拟）
const CURRENT_USER = {
    id: 'user001',
    name: '张三',
    role: '市级工单创建分派人员',
    department: '市12345热线中心',
    permissions: PERMISSION_CONFIG
};

// 导出配置对象
window.CONFIG = CONFIG;
window.CURRENT_USER = CURRENT_USER;
window.TICKET_STATUS = TICKET_STATUS;
window.PRIORITY_LEVEL = PRIORITY_LEVEL;
window.TICKET_TYPE = TICKET_TYPE;
window.CHANNEL_TYPE = CHANNEL_TYPE;
window.STATUS_CONFIG = STATUS_CONFIG;
window.PRIORITY_CONFIG = PRIORITY_CONFIG;
window.TYPE_CONFIG = TYPE_CONFIG;
window.CHANNEL_CONFIG = CHANNEL_CONFIG;
window.FILTER_CONFIG = FILTER_CONFIG;
window.PERMISSION_CONFIG = PERMISSION_CONFIG;
window.RESPONSIBLE_UNITS = RESPONSIBLE_UNITS;
window.MESSAGE_CONFIG = MESSAGE_CONFIG;
