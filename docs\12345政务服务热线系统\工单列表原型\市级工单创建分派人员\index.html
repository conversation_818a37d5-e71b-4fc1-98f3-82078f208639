<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市级工单创建分派人员 - 工单列表</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="fas fa-clipboard-list"></i>
                    工单列表管理
                </h1>
                <div class="user-info">
                    <span class="user-role">市级工单创建分派人员</span>
                    <span class="user-name">张三</span>
                </div>
            </div>
            <div class="header-right">
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-label">今日新增</span>
                        <span class="stat-value" id="todayCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">待处理</span>
                        <span class="stat-value" id="pendingCount">0</span>
                    </div>
                </div>
                <button class="btn btn-icon" id="refreshBtn" title="刷新列表">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 操作工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-primary" id="createTicketBtn">
                    <i class="fas fa-plus"></i>
                    新建工单
                </button>
                <div class="search-container">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索工单编号、标题、市民姓名或电话...">
                    <button class="btn btn-icon search-btn" id="searchBtn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <button class="btn btn-outline" id="advancedSearchBtn">
                    <i class="fas fa-filter"></i>
                    高级搜索
                </button>
            </div>
            <div class="toolbar-right">
                <div class="batch-actions" id="batchActions" style="display: none;">
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                    <button class="btn btn-outline" id="batchAssignBtn">
                        <i class="fas fa-share"></i>
                        批量指派
                    </button>
                    <button class="btn btn-outline" id="batchCloseBtn">
                        <i class="fas fa-check-circle"></i>
                        批量办结
                    </button>
                    <button class="btn btn-outline" id="exportBtn">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>

        <!-- 快捷筛选标签 -->
        <div class="filter-tabs">
            <div class="tab-item active" data-filter="myDrafts">
                <span class="tab-text">我的草稿</span>
                <span class="tab-badge" id="myDraftsCount">0</span>
            </div>
            <div class="tab-item" data-filter="returned">
                <span class="tab-text">被退回件</span>
                <span class="tab-badge" id="returnedCount">0</span>
            </div>
            <div class="tab-item" data-filter="assigned">
                <span class="tab-text">已派待接</span>
                <span class="tab-badge" id="assignedCount">0</span>
            </div>
            <div class="tab-item" data-filter="closed">
                <span class="tab-text">我已办结</span>
                <span class="tab-badge" id="closedCount">0</span>
            </div>
            <div class="tab-item" data-filter="today">
                <span class="tab-text">今日新增</span>
                <span class="tab-badge" id="todayTabCount">0</span>
            </div>
            <div class="tab-item" data-filter="urgent">
                <span class="tab-text">紧急/重大</span>
                <span class="tab-badge urgent" id="urgentCount">0</span>
            </div>
            <div class="tab-item" data-filter="all">
                <span class="tab-text">全部工单</span>
                <span class="tab-badge" id="allCount">0</span>
            </div>
        </div>

        <!-- 高级搜索面板 -->
        <div class="advanced-search-panel" id="advancedSearchPanel" style="display: none;">
            <div class="search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>工单编号</label>
                        <input type="text" id="searchTicketId" placeholder="请输入工单编号">
                    </div>
                    <div class="form-group">
                        <label>诉求标题</label>
                        <input type="text" id="searchTitle" placeholder="请输入诉求标题">
                    </div>
                    <div class="form-group">
                        <label>市民姓名</label>
                        <input type="text" id="searchCitizenName" placeholder="请输入市民姓名">
                    </div>
                    <div class="form-group">
                        <label>联系电话</label>
                        <input type="text" id="searchPhone" placeholder="请输入联系电话">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>工单状态</label>
                        <select id="searchStatus">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="pending">待接收</option>
                            <option value="processing">处理中</option>
                            <option value="closed">已关闭</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>工单类型</label>
                        <select id="searchType">
                            <option value="">全部类型</option>
                            <option value="complaint">投诉</option>
                            <option value="consultation">咨询</option>
                            <option value="suggestion">建议</option>
                            <option value="help">求助</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>紧急程度</label>
                        <select id="searchPriority">
                            <option value="">全部等级</option>
                            <option value="urgent">紧急</option>
                            <option value="high">重要</option>
                            <option value="normal">一般</option>
                            <option value="low">较低</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>诉求渠道</label>
                        <select id="searchChannel">
                            <option value="">全部渠道</option>
                            <option value="phone">电话</option>
                            <option value="website">网站</option>
                            <option value="app">APP</option>
                            <option value="wechat">微信</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>创建时间</label>
                        <div class="date-range">
                            <input type="date" id="searchStartDate">
                            <span>至</span>
                            <input type="date" id="searchEndDate">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>责任单位</label>
                        <select id="searchUnit">
                            <option value="">全部单位</option>
                            <option value="urban">城管局</option>
                            <option value="transport">交通局</option>
                            <option value="housing">住建局</option>
                            <option value="education">教育局</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>创建人</label>
                        <input type="text" id="searchCreator" placeholder="请输入创建人姓名">
                    </div>
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" id="doAdvancedSearch">
                        <i class="fas fa-search"></i>
                        查询
                    </button>
                    <button class="btn btn-outline" id="resetSearch">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn btn-outline" id="collapseSearch">
                        <i class="fas fa-chevron-up"></i>
                        收起
                    </button>
                </div>
            </div>
        </div>

        <!-- 工单列表表格 -->
        <div class="table-container">
            <table class="ticket-table" id="ticketTable">
                <thead>
                    <tr>
                        <th class="checkbox-col">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th class="sortable" data-sort="woId">
                            工单编号
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="title">
                            诉求标题
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="status">
                            当前状态
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="priority">
                            紧急程度
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="channel">
                            诉求渠道
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="type">
                            工单类型
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="citizenName">
                            市民姓名
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="creatorName">
                            创建人
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="createTime">
                            创建时间
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="responsibleUnit">
                            责任单位
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="sortable" data-sort="remainingTime">
                            剩余时间
                            <i class="fas fa-sort"></i>
                        </th>
                        <th class="actions-col">操作</th>
                    </tr>
                </thead>
                <tbody id="ticketTableBody">
                    <!-- 表格内容将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span>共 <span id="totalCount">0</span> 条记录，每页显示</span>
                <select id="pageSizeSelect">
                    <option value="20">20</option>
                    <option value="50" selected>50</option>
                    <option value="100">100</option>
                </select>
                <span>条</span>
            </div>
            <div class="pagination" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </main>

    <!-- 快速预览面板 -->
    <div class="preview-panel" id="previewPanel" style="display: none;">
        <div class="preview-overlay" id="previewOverlay"></div>
        <div class="preview-content">
            <!-- 顶部标题栏 -->
            <div class="preview-header">
                <div class="preview-title-section">
                    <div class="preview-ticket-id" id="previewTicketId">SZ20250728-00001</div>
                    <div class="preview-ticket-title" id="previewTicketTitle">关于XX小区夜间施工噪音扰民的投诉</div>
                    <div class="preview-tags" id="previewTags">
                        <!-- 标签将动态生成 -->
                    </div>
                </div>
                <button class="preview-close-btn" id="previewCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 核心操作区 -->
            <div class="preview-actions" id="previewActions">
                <!-- 操作按钮将根据工单状态动态生成 -->
            </div>

            <!-- 关键信息区 -->
            <div class="preview-body">
                <!-- 一、核心诉求详情 -->
                <div class="preview-section">
                    <h3 class="section-title">一、核心诉求详情</h3>
                    <div class="section-content">
                        <div class="info-item">
                            <label>诉求内容：</label>
                            <div class="content-text" id="previewContent">
                                <!-- 诉求内容 -->
                            </div>
                        </div>
                        <div class="info-item">
                            <label>附件列表：</label>
                            <div class="attachments-list" id="previewAttachments">
                                <!-- 附件列表 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 二、市民与位置信息 -->
                <div class="preview-section">
                    <h3 class="section-title">二、市民与位置信息</h3>
                    <div class="section-content">
                        <div class="citizen-info-row">
                            <div class="info-item">
                                <label>市民姓名：</label>
                                <span id="previewCitizenName">王**</span>
                            </div>
                            <div class="info-item">
                                <label>联系电话：</label>
                                <span id="previewCitizenPhone">138****1234</span>
                                <button class="btn-copy" title="复制电话">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="citizen-info-row">
                            <div class="info-item">
                                <label>历史工单：</label>
                                <span id="previewHistoryCount">3</span> 次
                            </div>
                            <div class="info-item">
                                <label>历史满意度：</label>
                                <span id="previewSatisfaction">67%</span>
                                <button class="btn-link" id="viewHistoryBtn">查看历史</button>
                            </div>
                        </div>
                        <div class="info-item">
                            <label>详细地址：</label>
                            <span id="previewAddress">XX区XX路XX小区3栋</span>
                        </div>
                        <div class="info-item">
                            <label>地图定位：</label>
                            <div class="map-container" id="previewMap">
                                <!-- 地图将在这里显示 -->
                                <div class="map-placeholder">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>点击查看地图位置</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 三、工单分类属性 -->
                <div class="preview-section">
                    <h3 class="section-title">三、工单分类属性</h3>
                    <div class="section-content">
                        <div class="classification-row">
                            <div class="info-item">
                                <label>工单类型：</label>
                                <span id="previewType">投诉</span>
                            </div>
                            <div class="info-item">
                                <label>业务分类：</label>
                                <span id="previewCategory">城市管理 → 工地管理 → 工地噪音</span>
                            </div>
                        </div>
                        <div class="classification-row">
                            <div class="info-item">
                                <label>紧急程度：</label>
                                <span id="previewPriority">紧急</span>
                            </div>
                            <div class="info-item">
                                <label>诉求渠道：</label>
                                <span id="previewChannel">电话</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 四、历史追溯区 -->
                <div class="preview-section">
                    <h3 class="section-title">四、历史追溯</h3>
                    <div class="section-content">
                        <div class="return-reason" id="previewReturnReason" style="display: none;">
                            <label>退回原因：</label>
                            <div class="return-content">
                                <!-- 退回原因内容 -->
                            </div>
                        </div>
                        <div class="info-item">
                            <label>流转历史：</label>
                            <div class="flow-history" id="previewFlowHistory">
                                <!-- 流转历史 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/preview-data.js"></script>
    <script src="js/preview-controller.js"></script>
    <script src="js/controller.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
