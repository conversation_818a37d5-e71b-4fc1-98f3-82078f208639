/**
 * 基础样式和重置
 * 包含CSS重置、基础变量定义、通用样式
 */

/* CSS重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 14px;
    line-height: 1.6;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
}

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    --primary-light: #e6f7ff;

    /* 辅助色 */
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;

    /* 中性色 - 固定为明亮主题 */
    --text-color: #262626;
    --text-secondary: #595959;
    --text-disabled: #bfbfbf;
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;
    --bg-color: #f5f5f5;
    --bg-white: #ffffff;
    --bg-gray: #fafafa;
    
    /* 紧急程度颜色 */
    --priority-normal: #52c41a;
    --priority-urgent: #faad14;
    --priority-critical: #ff4d4f;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 圆角 */
    --border-radius-sm: 2px;
    --border-radius-md: 4px;
    --border-radius-lg: 6px;
    --border-radius-xl: 8px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.03);
    --shadow-md: 0 1px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    /* 字体粗细 */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 过渡动画 */
    --transition-fast: 0.1s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
}

/* 通用样式 */
.clearfix::after {
    content: '';
    display: table;
    clear: both;
}

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-secondary { color: var(--text-secondary); }
.text-disabled { color: var(--text-disabled); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-white { background-color: var(--bg-white); }
.bg-gray { background-color: var(--bg-gray); }

.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-light); }
.border-primary { border: 1px solid var(--primary-color); }
.border-success { border: 1px solid var(--success-color); }
.border-warning { border: 1px solid var(--warning-color); }
.border-error { border: 1px solid var(--error-color); }

.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-gray);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 选择文本样式 */
::selection {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

/* 焦点样式 */
:focus {
    outline: none;
}

:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 禁用状态 */
.disabled,
[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid var(--border-light);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 隐藏元素但保持可访问性 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
