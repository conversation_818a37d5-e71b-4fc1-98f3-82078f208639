/**
 * UI组件交互模块
 * 包含各种UI组件的交互逻辑和事件处理
 */

class UIComponents {
    constructor() {
        this.attachments = [];
        this.selectedBusinessTags = new Set();
        this.customTags = [];
        this.selectedDepartments = new Set();
        this.departments = []; // 存储部门数据
        this.addressSuggestions = [];

        this.initComponents();
    }

    /**
     * 初始化所有组件
     */
    initComponents() {
        this.initProcessModeToggle();
        this.initDepartmentSelection();
        this.initCategorySelection();
        this.initTagComponents();
        this.initAddressInput();
        this.initFileUpload();
        this.initModalComponents();
        this.initCharacterCounters();
        this.initCopyButtons();
        this.initFormActions();
    }

    /**
     * 初始化处理模式切换
     */
    initProcessModeToggle() {
        const processModeInputs = document.querySelectorAll('input[name="processMode"]');
        const processModeSection = document.getElementById('processModeInfo');
        const instantFinishSection = document.getElementById('instantFinishSection');
        const submitButton = document.getElementById('submitTicket');

        processModeInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const mode = e.target.value;

                if (mode === '派单处理') {
                    processModeSection.classList.add('dispatch-mode');
                    processModeSection.classList.remove('instant-finish');
                    instantFinishSection.style.display = 'none';

                    // 恢复提交按钮文字
                    submitButton.textContent = '提交工单';
                    submitButton.className = 'btn btn-primary';

                } else if (mode === '即时办结') {
                    processModeSection.classList.remove('dispatch-mode');
                    processModeSection.classList.add('instant-finish');
                    instantFinishSection.style.display = 'block';

                    // 修改提交按钮文字和样式
                    submitButton.textContent = '即时办结';
                    submitButton.className = 'btn btn-success';
                }

                console.log('处理模式切换:', mode);
            });
        });

        // 初始化时设置默认状态
        const checkedMode = document.querySelector('input[name="processMode"]:checked');
        if (checkedMode) {
            checkedMode.dispatchEvent(new Event('change'));
        }
    }

    /**
     * 初始化部门选择
     */
    initDepartmentSelection() {
        const selectedDepartments = document.getElementById('selectedDepartments');
        const departmentDropdown = document.getElementById('departmentDropdown');
        const departmentSearch = document.getElementById('departmentSearch');
        const departmentList = document.getElementById('departmentList');
        const basicInfoSection = document.getElementById('basicInfo');

        // 加载部门数据
        this.loadDepartments();

        // 点击选中部门区域显示下拉框
        selectedDepartments.addEventListener('click', () => {
            this.toggleDepartmentDropdown();
        });

        // 搜索部门
        departmentSearch.addEventListener('input', (e) => {
            this.filterDepartments(e.target.value);
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.department-selection')) {
                this.hideDepartmentDropdown();
            }
        });

        // 主办单位变更
        const mainDepartmentSelect = document.getElementById('mainDepartment');
        mainDepartmentSelect.addEventListener('change', () => {
            this.updateCoDepartmentList();
        });
    }

    /**
     * 加载部门数据
     */
    async loadDepartments() {
        try {
            const response = await MockAPI.getDepartments();
            if (response.success) {
                this.departments = response.data;
                this.renderDepartmentList();
            }
        } catch (error) {
            console.error('加载部门数据失败:', error);
        }
    }

    /**
     * 渲染部门列表
     */
    renderDepartmentList(filteredDepartments = null) {
        const departmentList = document.getElementById('departmentList');
        const departments = filteredDepartments || this.departments || [];

        departmentList.innerHTML = '';

        if (departments.length === 0) {
            departmentList.innerHTML = '<div class="department-item disabled">暂无匹配的部门</div>';
            return;
        }

        departments.forEach(dept => {
            const item = document.createElement('div');
            item.className = 'department-item';
            item.dataset.deptId = dept.id;

            if (this.selectedDepartments.has(dept.id)) {
                item.classList.add('selected');
            }

            item.innerHTML = `
                <div>
                    <span class="department-name">${dept.name}</span>
                    <span class="department-code">(${dept.code})</span>
                </div>
                <span class="department-type">${dept.type}</span>
            `;

            item.addEventListener('click', () => {
                this.toggleDepartmentSelection(dept.id);
            });

            departmentList.appendChild(item);
        });
    }

    /**
     * 切换部门选择状态
     */
    toggleDepartmentSelection(deptId) {
        if (this.selectedDepartments.has(deptId)) {
            this.selectedDepartments.delete(deptId);
        } else {
            this.selectedDepartments.add(deptId);
        }

        this.updateSelectedDepartmentsDisplay();
        this.renderDepartmentList();
        this.updateMultiDepartmentMode();
    }

    /**
     * 更新已选部门显示
     */
    updateSelectedDepartmentsDisplay() {
        const selectedDepartmentsEl = document.getElementById('selectedDepartments');

        if (this.selectedDepartments.size === 0) {
            selectedDepartmentsEl.innerHTML = '<div class="department-placeholder">请选择承办单位</div>';
            return;
        }

        selectedDepartmentsEl.innerHTML = '';

        this.selectedDepartments.forEach(deptId => {
            const dept = this.departments.find(d => d.id === deptId);
            if (dept) {
                const tag = document.createElement('div');
                tag.className = 'selected-department-tag';
                tag.innerHTML = `
                    <span>${dept.name}</span>
                    <span class="remove-department" data-dept-id="${deptId}">×</span>
                `;

                tag.querySelector('.remove-department').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDepartmentSelection(deptId);
                });

                selectedDepartmentsEl.appendChild(tag);
            }
        });
    }

    /**
     * 更新多部门模式
     */
    updateMultiDepartmentMode() {
        const basicInfoSection = document.getElementById('basicInfo');
        const isMultiDept = this.selectedDepartments.size > 1;

        console.log('更新多部门模式:', {
            selectedCount: this.selectedDepartments.size,
            isMultiDept: isMultiDept,
            selectedDepts: Array.from(this.selectedDepartments)
        });

        if (isMultiDept) {
            basicInfoSection.classList.add('multi-department');
            this.updateMainDepartmentOptions();
            this.showMessage(`已选择${this.selectedDepartments.size}个部门，请指定主办单位`, 'info');
        } else {
            basicInfoSection.classList.remove('multi-department');
            // 清空主办单位选择
            const mainDepartmentSelect = document.getElementById('mainDepartment');
            mainDepartmentSelect.value = '';
            this.updateCoDepartmentList();
        }
    }

    /**
     * 更新主办单位选项
     */
    updateMainDepartmentOptions() {
        const mainDepartmentSelect = document.getElementById('mainDepartment');

        // 清空选项
        mainDepartmentSelect.innerHTML = '<option value="">请选择主办单位</option>';

        // 添加已选择的部门
        this.selectedDepartments.forEach(deptId => {
            const dept = this.departments.find(d => d.id === deptId);
            if (dept) {
                const option = new Option(dept.name, dept.id);
                mainDepartmentSelect.add(option);
            }
        });

        console.log('更新主办单位选项:', {
            departmentCount: this.selectedDepartments.size,
            departments: Array.from(this.selectedDepartments)
        });

        // 触发协办单位列表更新
        this.updateCoDepartmentList();
    }

    /**
     * 更新协办单位列表
     */
    updateCoDepartmentList() {
        const coDepartmentList = document.getElementById('coDepartmentList');
        const mainDepartment = document.getElementById('mainDepartment').value;

        // 清空列表
        coDepartmentList.innerHTML = '';

        const coDepartments = Array.from(this.selectedDepartments).filter(deptId => deptId !== mainDepartment);

        console.log('更新协办单位列表:', {
            mainDepartment: mainDepartment,
            allSelected: Array.from(this.selectedDepartments),
            coDepartments: coDepartments
        });

        if (coDepartments.length === 0) {
            if (this.selectedDepartments.size > 1 && !mainDepartment) {
                coDepartmentList.innerHTML = '<div class="empty-co-departments">请先选择主办单位</div>';
            } else {
                coDepartmentList.innerHTML = '<div class="empty-co-departments">暂无协办单位</div>';
            }
            return;
        }

        coDepartments.forEach(deptId => {
            const dept = this.departments.find(d => d.id === deptId);
            if (dept) {
                const tag = document.createElement('span');
                tag.className = 'tag-list-item';
                tag.textContent = dept.name;
                coDepartmentList.appendChild(tag);
            }
        });
    }

    /**
     * 切换部门下拉框显示
     */
    toggleDepartmentDropdown() {
        const dropdown = document.getElementById('departmentDropdown');
        const selectedDepartments = document.getElementById('selectedDepartments');

        if (dropdown.classList.contains('show')) {
            this.hideDepartmentDropdown();
        } else {
            dropdown.classList.add('show');
            selectedDepartments.classList.add('active');
            document.getElementById('departmentSearch').focus();
        }
    }

    /**
     * 隐藏部门下拉框
     */
    hideDepartmentDropdown() {
        const dropdown = document.getElementById('departmentDropdown');
        const selectedDepartments = document.getElementById('selectedDepartments');

        dropdown.classList.remove('show');
        selectedDepartments.classList.remove('active');
        document.getElementById('departmentSearch').value = '';
        this.renderDepartmentList(); // 重置列表
    }

    /**
     * 过滤部门
     */
    filterDepartments(keyword) {
        if (!keyword.trim()) {
            this.renderDepartmentList();
            return;
        }

        const filtered = this.departments.filter(dept =>
            dept.name.includes(keyword) ||
            dept.code.toLowerCase().includes(keyword.toLowerCase())
        );

        this.renderDepartmentList(filtered);
    }

    /**
     * 初始化分类选择
     */
    initCategorySelection() {
        const category1Select = document.getElementById('category1');
        const category2Select = document.getElementById('category2');
        const category3Select = document.getElementById('category3');

        // 加载一级分类
        this.loadCategories();

        // 一级分类变更
        category1Select.addEventListener('change', async (e) => {
            const category1 = e.target.value;
            category2Select.innerHTML = '<option value="">请选择二级分类</option>';
            category3Select.innerHTML = '<option value="">请先选择二级分类</option>';
            
            if (category1) {
                category2Select.disabled = false;
                const response = await MockAPI.getCategories(category1);
                if (response.success) {
                    response.data.forEach(cat => {
                        category2Select.add(new Option(cat, cat));
                    });
                }
            } else {
                category2Select.disabled = true;
                category3Select.disabled = true;
            }
        });

        // 二级分类变更
        category2Select.addEventListener('change', async (e) => {
            const category2 = e.target.value;
            category3Select.innerHTML = '<option value="">请选择三级分类</option>';
            
            if (category2) {
                category3Select.disabled = false;
                const response = await MockAPI.getCategories(category2);
                if (response.success) {
                    response.data.forEach(cat => {
                        category3Select.add(new Option(cat, cat));
                    });
                }
            } else {
                category3Select.disabled = true;
            }
        });
    }

    /**
     * 加载分类数据
     */
    async loadCategories() {
        try {
            const response = await MockAPI.getCategories();
            if (response.success) {
                const category1Select = document.getElementById('category1');
                response.data.forEach(cat => {
                    category1Select.add(new Option(cat, cat));
                });
            }
        } catch (error) {
            console.error('加载分类数据失败:', error);
        }
    }

    /**
     * 初始化标签组件
     */
    initTagComponents() {
        this.initBusinessTags();
        this.initCustomTags();
    }

    /**
     * 初始化业务标签
     */
    initBusinessTags() {
        const businessTagsContainer = document.getElementById('businessTags');
        
        CONSTANTS.BUSINESS_TAGS.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag-item';
            tagElement.textContent = tag;
            tagElement.dataset.value = tag;
            
            tagElement.addEventListener('click', () => {
                if (this.selectedBusinessTags.has(tag)) {
                    this.selectedBusinessTags.delete(tag);
                    tagElement.classList.remove('selected');
                } else if (this.selectedBusinessTags.size < CONFIG.form.maxBusinessTags) {
                    this.selectedBusinessTags.add(tag);
                    tagElement.classList.add('selected');
                } else {
                    this.showMessage('最多只能选择5个业务标签', 'warning');
                }
                
                this.updateTagStates();
            });
            
            businessTagsContainer.appendChild(tagElement);
        });
    }

    /**
     * 初始化自定义标签
     */
    initCustomTags() {
        const customTagInput = document.getElementById('customTagInput');
        const addTagButton = document.querySelector('.add-tag-btn');
        const customTagsContainer = document.getElementById('customTags');

        const addCustomTag = () => {
            const tagText = customTagInput.value.trim();

            if (!tagText) {
                this.showMessage('请输入标签内容', 'warning');
                return;
            }

            if (tagText.length < 2 || tagText.length > 10) {
                this.showMessage('标签长度应在2-10个字符之间', 'warning');
                return;
            }

            if (this.customTags.includes(tagText)) {
                this.showMessage('标签已存在', 'warning');
                return;
            }

            if (this.customTags.length >= CONFIG.form.maxCustomTags) {
                this.showMessage('最多只能添加3个自定义标签', 'warning');
                return;
            }

            this.customTags.push(tagText);
            this.renderCustomTags();
            customTagInput.value = '';
        };

        addTagButton.addEventListener('click', addCustomTag);
        customTagInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                addCustomTag();
            }
        });
    }

    /**
     * 渲染自定义标签
     */
    renderCustomTags() {
        const customTagsContainer = document.getElementById('customTags');
        customTagsContainer.innerHTML = '';
        
        this.customTags.forEach((tag, index) => {
            const tagElement = document.createElement('span');
            tagElement.className = 'custom-tag';
            tagElement.innerHTML = `
                ${tag}
                <span class="remove-tag" data-index="${index}">×</span>
            `;
            
            tagElement.querySelector('.remove-tag').addEventListener('click', () => {
                this.customTags.splice(index, 1);
                this.renderCustomTags();
            });
            
            customTagsContainer.appendChild(tagElement);
        });
    }

    /**
     * 更新标签状态
     */
    updateTagStates() {
        const businessTags = document.querySelectorAll('#businessTags .tag-item');
        const isMaxSelected = this.selectedBusinessTags.size >= CONFIG.form.maxBusinessTags;
        
        businessTags.forEach(tag => {
            if (!tag.classList.contains('selected')) {
                tag.classList.toggle('disabled', isMaxSelected);
            }
        });
    }

    /**
     * 初始化地址输入
     */
    initAddressInput() {
        this.initAdministrativeDivisions();
        this.initDetailAddressInput();
        this.initMapComponent();
        this.initHistoryAddresses();
    }

    /**
     * 初始化行政区划级联选择
     */
    initAdministrativeDivisions() {
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const districtSelect = document.getElementById('district');
        const streetSelect = document.getElementById('street');
        const communitySelect = document.getElementById('community');

        // 加载省份数据
        this.loadProvinces();

        // 省份变更
        provinceSelect.addEventListener('change', async (e) => {
            const provinceCode = e.target.value;
            this.resetCascadeSelects(['city', 'district', 'street', 'community']);

            if (provinceCode) {
                citySelect.disabled = false;
                const response = await MockAPI.getCities(provinceCode);
                if (response.success) {
                    this.populateSelect(citySelect, response.data, '请选择城市');
                }
            } else {
                citySelect.disabled = true;
            }

            this.updateFullAddress();
        });

        // 城市变更
        citySelect.addEventListener('change', async (e) => {
            const cityCode = e.target.value;
            this.resetCascadeSelects(['district', 'street', 'community']);

            if (cityCode) {
                districtSelect.disabled = false;
                const response = await MockAPI.getDistricts(cityCode);
                if (response.success) {
                    this.populateSelect(districtSelect, response.data, '请选择区/县');
                }
            } else {
                districtSelect.disabled = true;
            }

            this.updateFullAddress();
        });

        // 区县变更
        districtSelect.addEventListener('change', async (e) => {
            const districtCode = e.target.value;
            this.resetCascadeSelects(['street', 'community']);

            if (districtCode) {
                streetSelect.disabled = false;
                const response = await MockAPI.getStreets(districtCode);
                if (response.success) {
                    this.populateSelect(streetSelect, response.data, '请选择街道/乡镇');
                }
            } else {
                streetSelect.disabled = true;
            }

            this.updateFullAddress();
        });

        // 街道变更
        streetSelect.addEventListener('change', async (e) => {
            const streetCode = e.target.value;
            this.resetCascadeSelects(['community']);

            if (streetCode) {
                communitySelect.disabled = false;
                const response = await MockAPI.getCommunities(streetCode);
                if (response.success) {
                    this.populateSelect(communitySelect, response.data, '请选择社区/村');
                }
            } else {
                communitySelect.disabled = true;
            }

            this.updateFullAddress();
        });

        // 社区变更
        communitySelect.addEventListener('change', () => {
            this.updateFullAddress();
        });
    }

    /**
     * 加载省份数据
     */
    async loadProvinces() {
        try {
            const response = await MockAPI.getProvinces();
            if (response.success) {
                const provinceSelect = document.getElementById('province');
                this.populateSelect(provinceSelect, response.data, '请选择省份');

                // 默认选择广东省（演示用）
                provinceSelect.value = '440000';
                provinceSelect.dispatchEvent(new Event('change'));
            }
        } catch (error) {
            console.error('加载省份数据失败:', error);
        }
    }

    /**
     * 填充下拉选择框
     */
    populateSelect(selectElement, data, placeholder) {
        selectElement.innerHTML = `<option value="">${placeholder}</option>`;
        data.forEach(item => {
            const option = new Option(item.name, item.code);
            selectElement.add(option);
        });
    }

    /**
     * 重置级联选择框
     */
    resetCascadeSelects(selectIds) {
        selectIds.forEach(id => {
            const select = document.getElementById(id);
            select.innerHTML = `<option value="">请先选择上级区划</option>`;
            select.disabled = true;
        });
    }

    /**
     * 更新完整地址显示
     */
    updateFullAddress() {
        const addressData = this.getSelectedAddressData();
        console.log('当前选择的地址:', addressData);

        // 可以在这里更新地图定位等
        if (addressData.province && addressData.city && addressData.district) {
            this.updateMapLocation(addressData);
        }
    }

    /**
     * 获取当前选择的地址数据
     */
    getSelectedAddressData() {
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const districtSelect = document.getElementById('district');
        const streetSelect = document.getElementById('street');
        const communitySelect = document.getElementById('community');
        const detailInput = document.getElementById('detailAddress');

        return {
            province: provinceSelect.value,
            provinceName: provinceSelect.options[provinceSelect.selectedIndex]?.text || '',
            city: citySelect.value,
            cityName: citySelect.options[citySelect.selectedIndex]?.text || '',
            district: districtSelect.value,
            districtName: districtSelect.options[districtSelect.selectedIndex]?.text || '',
            street: streetSelect.value,
            streetName: streetSelect.options[streetSelect.selectedIndex]?.text || '',
            community: communitySelect.value,
            communityName: communitySelect.options[communitySelect.selectedIndex]?.text || '',
            detail: detailInput.value
        };
    }

    /**
     * 初始化详细地址输入
     */
    initDetailAddressInput() {
        const detailInput = document.getElementById('detailAddress');
        const addressSuggestions = document.getElementById('addressSuggestions');

        // 详细地址输入建议
        const debouncedSearch = UTILS.debounce(async (keyword) => {
            if (keyword.length >= 2) {
                const location = this.getSelectedAddressData();
                const response = await MockAPI.searchDetailAddresses(keyword, location);
                if (response.success) {
                    this.showAddressSuggestions(response.data);
                }
            } else {
                this.hideAddressSuggestions();
            }
        }, 300);

        detailInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value);
            this.updateCharacterCounter('detailAddress', 'detailAddressCounter');
        });

        // 点击外部隐藏建议
        document.addEventListener('click', (e) => {
            if (!detailInput.contains(e.target) && !addressSuggestions.contains(e.target)) {
                this.hideAddressSuggestions();
            }
        });
    }

    /**
     * 初始化地图组件
     */
    initMapComponent() {
        const mapButton = document.querySelector('.map-btn');
        const mapContainer = document.getElementById('mapContainer');

        mapButton.addEventListener('click', () => {
            const isVisible = mapContainer.style.display !== 'none';
            mapContainer.style.display = isVisible ? 'none' : 'block';
            const btnText = mapButton.querySelector('.btn-text');
            btnText.textContent = isVisible ? '地图选择' : '隐藏地图';

            if (!isVisible) {
                // 显示地图时更新位置
                const addressData = this.getSelectedAddressData();
                this.updateMapLocation(addressData);
            }
        });
    }

    /**
     * 更新地图位置
     */
    updateMapLocation(addressData) {
        const coordinatesEl = document.getElementById('mapCoordinates');
        if (coordinatesEl && addressData.province && addressData.city && addressData.district) {
            // 模拟坐标更新
            const lng = 113.324520 + (Math.random() - 0.5) * 0.1;
            const lat = 23.129163 + (Math.random() - 0.5) * 0.1;

            coordinatesEl.innerHTML = `
                <span>经度：${lng.toFixed(6)}</span>
                <span>纬度：${lat.toFixed(6)}</span>
            `;
        }
    }

    /**
     * 初始化历史地址
     */
    async initHistoryAddresses() {
        try {
            const response = await MockAPI.getHistoryAddresses();
            if (response.success) {
                this.renderHistoryAddresses(response.data);
            }
        } catch (error) {
            console.error('加载历史地址失败:', error);
        }
    }

    /**
     * 渲染历史地址
     */
    renderHistoryAddresses(addresses) {
        const container = document.getElementById('historyAddresses');
        container.innerHTML = '';

        if (addresses.length === 0) {
            container.innerHTML = '<div class="empty-history">暂无历史地址</div>';
            return;
        }

        addresses.slice(0, 5).forEach(addr => {
            const item = document.createElement('div');
            item.className = 'history-address-item';
            item.innerHTML = `
                <div class="address-text">${addr.fullAddress}</div>
                <div class="address-frequency">使用${addr.frequency}次</div>
            `;

            item.addEventListener('click', () => {
                this.selectHistoryAddress(addr);
            });

            container.appendChild(item);
        });
    }

    /**
     * 选择历史地址
     */
    async selectHistoryAddress(addressData) {
        // 设置行政区划选择
        if (addressData.province) {
            document.getElementById('province').value = addressData.province;
            document.getElementById('province').dispatchEvent(new Event('change'));

            // 等待城市加载完成后设置
            setTimeout(async () => {
                if (addressData.city) {
                    document.getElementById('city').value = addressData.city;
                    document.getElementById('city').dispatchEvent(new Event('change'));

                    setTimeout(async () => {
                        if (addressData.district) {
                            document.getElementById('district').value = addressData.district;
                            document.getElementById('district').dispatchEvent(new Event('change'));

                            setTimeout(async () => {
                                if (addressData.street) {
                                    document.getElementById('street').value = addressData.street;
                                    document.getElementById('street').dispatchEvent(new Event('change'));

                                    setTimeout(() => {
                                        if (addressData.community) {
                                            document.getElementById('community').value = addressData.community;
                                        }
                                    }, 300);
                                }
                            }, 300);
                        }
                    }, 300);
                }
            }, 300);
        }

        // 设置详细地址
        if (addressData.detail) {
            document.getElementById('detailAddress').value = addressData.detail;
            this.updateCharacterCounter('detailAddress', 'detailAddressCounter');
        }

        this.showMessage('已选择历史地址', 'success');
    }

    /**
     * 更新字符计数器
     */
    updateCharacterCounter(inputId, counterId) {
        const input = document.getElementById(inputId);
        const counter = document.getElementById(counterId);

        if (input && counter) {
            counter.textContent = input.value.length;
        }
    }

    /**
     * 显示地址建议
     */
    showAddressSuggestions(suggestions) {
        const addressSuggestions = document.getElementById('addressSuggestions');
        addressSuggestions.innerHTML = '';
        
        suggestions.forEach(address => {
            const item = document.createElement('div');
            item.className = 'address-suggestion-item';
            item.textContent = address;
            
            item.addEventListener('click', () => {
                document.getElementById('address').value = address;
                this.hideAddressSuggestions();
            });
            
            addressSuggestions.appendChild(item);
        });
        
        addressSuggestions.classList.add('show');
    }

    /**
     * 隐藏地址建议
     */
    hideAddressSuggestions() {
        const addressSuggestions = document.getElementById('addressSuggestions');
        addressSuggestions.classList.remove('show');
    }

    /**
     * 初始化文件上传
     */
    initFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const selectFilesButton = document.getElementById('selectFiles');

        // 点击选择文件
        selectFilesButton.addEventListener('click', () => {
            fileInput.click();
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileSelection(e.dataTransfer.files);
        });

        // 粘贴上传
        document.addEventListener('paste', (e) => {
            const items = e.clipboardData.items;
            const files = [];
            
            for (const item of items) {
                if (item.type.indexOf('image') !== -1) {
                    files.push(item.getAsFile());
                }
            }
            
            if (files.length > 0) {
                this.handleFileSelection(files);
            }
        });
    }

    /**
     * 处理文件选择
     */
    async handleFileSelection(files) {
        const fileArray = Array.from(files);
        
        // 验证文件数量
        if (this.attachments.length + fileArray.length > CONFIG.form.maxFileCount) {
            this.showMessage(`最多只能上传${CONFIG.form.maxFileCount}个文件`, 'error');
            return;
        }

        // 验证文件
        for (const file of fileArray) {
            const validation = UTILS.validateFile(file);
            if (!validation.valid) {
                this.showMessage(validation.message, 'error');
                return;
            }
        }

        // 添加文件
        for (const file of fileArray) {
            await this.addAttachment(file);
        }
    }

    /**
     * 添加附件
     */
    async addAttachment(file) {
        const attachment = {
            id: 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            file: file,
            name: file.name,
            size: file.size,
            type: UTILS.getFileType(file.name),
            description: '',
            uploadProgress: 0,
            uploaded: false
        };

        this.attachments.push(attachment);
        this.renderAttachments();

        // 模拟上传
        try {
            await this.uploadFile(attachment);
        } catch (error) {
            this.showMessage('文件上传失败', 'error');
            this.removeAttachment(attachment.id);
        }
    }

    /**
     * 上传文件
     */
    async uploadFile(attachment) {
        const progressCallback = (progress) => {
            attachment.uploadProgress = progress;
            this.updateAttachmentProgress(attachment.id, progress);
        };

        // 模拟上传进度
        const uploadInterval = setInterval(() => {
            attachment.uploadProgress += Math.random() * 20;
            if (attachment.uploadProgress >= 100) {
                attachment.uploadProgress = 100;
                attachment.uploaded = true;
                clearInterval(uploadInterval);
                this.updateAttachmentProgress(attachment.id, 100);
            } else {
                this.updateAttachmentProgress(attachment.id, attachment.uploadProgress);
            }
        }, 200);

        // 调用上传API
        const response = await MockAPI.uploadFile(attachment.file);
        if (response.success) {
            attachment.url = response.data.fileUrl;
            attachment.uploaded = true;
        }
    }

    /**
     * 更新附件上传进度
     */
    updateAttachmentProgress(attachmentId, progress) {
        const progressBar = document.querySelector(`[data-attachment-id="${attachmentId}"] .upload-progress-bar`);
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            if (progress >= 100) {
                progressBar.classList.add('success');
            }
        }
    }

    /**
     * 渲染附件列表
     */
    renderAttachments() {
        const attachmentList = document.getElementById('attachmentList');
        attachmentList.innerHTML = '';

        this.attachments.forEach(attachment => {
            const attachmentCard = this.createAttachmentCard(attachment);
            attachmentList.appendChild(attachmentCard);
        });
    }

    /**
     * 创建附件卡片
     */
    createAttachmentCard(attachment) {
        const card = document.createElement('div');
        card.className = 'attachment-card';
        card.dataset.attachmentId = attachment.id;

        const preview = this.createAttachmentPreview(attachment);
        const info = this.createAttachmentInfo(attachment);
        const actions = this.createAttachmentActions(attachment);

        card.appendChild(preview);
        card.appendChild(info);
        card.appendChild(actions);

        return card;
    }

    /**
     * 创建附件预览
     */
    createAttachmentPreview(attachment) {
        const preview = document.createElement('div');
        preview.className = 'attachment-preview';

        if (attachment.type === 'image') {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(attachment.file);
            img.alt = attachment.name;
            preview.appendChild(img);
        } else {
            const icon = document.createElement('div');
            icon.className = 'file-icon';
            icon.textContent = this.getFileIcon(attachment.type);
            preview.appendChild(icon);
        }

        return preview;
    }

    /**
     * 创建附件信息
     */
    createAttachmentInfo(attachment) {
        const info = document.createElement('div');
        info.className = 'attachment-info';

        info.innerHTML = `
            <div class="attachment-name">${attachment.name}</div>
            <div class="attachment-size">${UTILS.formatFileSize(attachment.size)}</div>
            <div class="attachment-description">
                <input type="text" placeholder="添加描述..." value="${attachment.description}">
            </div>
            <div class="upload-progress">
                <div class="upload-progress-bar" style="width: ${attachment.uploadProgress}%"></div>
            </div>
        `;

        // 描述输入事件
        const descInput = info.querySelector('input');
        descInput.addEventListener('input', (e) => {
            attachment.description = e.target.value;
        });

        return info;
    }

    /**
     * 创建附件操作
     */
    createAttachmentActions(attachment) {
        const actions = document.createElement('div');
        actions.className = 'attachment-actions';

        actions.innerHTML = `
            <button type="button" class="btn btn-small btn-outline">预览</button>
            <button type="button" class="btn btn-small btn-secondary">下载</button>
            <button type="button" class="btn btn-small btn-danger">删除</button>
        `;

        // 绑定事件
        const [previewBtn, downloadBtn, deleteBtn] = actions.querySelectorAll('button');

        previewBtn.addEventListener('click', () => {
            this.previewAttachment(attachment);
        });

        downloadBtn.addEventListener('click', () => {
            this.downloadAttachment(attachment);
        });

        deleteBtn.addEventListener('click', () => {
            this.removeAttachment(attachment.id);
        });

        return actions;
    }

    /**
     * 获取文件图标
     */
    getFileIcon(type) {
        const icons = {
            image: '🖼️',
            document: '📄',
            audio: '🎵',
            video: '🎬',
            unknown: '📎'
        };
        return icons[type] || icons.unknown;
    }

    /**
     * 预览附件
     */
    previewAttachment(attachment) {
        if (attachment.type === 'image') {
            const modal = this.createImagePreviewModal(attachment);
            document.body.appendChild(modal);
            this.showModal(modal);
        } else {
            this.showMessage('该文件类型不支持预览', 'info');
        }
    }

    /**
     * 下载附件
     */
    downloadAttachment(attachment) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(attachment.file);
        link.download = attachment.name;
        link.click();
    }

    /**
     * 移除附件
     */
    removeAttachment(attachmentId) {
        this.attachments = this.attachments.filter(att => att.id !== attachmentId);
        this.renderAttachments();
    }

    /**
     * 初始化模态框组件
     */
    initModalComponents() {
        // 关闭按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-close') || e.target.id === 'closePreview' || e.target.id === 'closePreviewBtn') {
                this.hideModal(e.target.closest('.modal'));
            }
        });

        // 点击遮罩关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target);
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const visibleModal = document.querySelector('.modal.show');
                if (visibleModal) {
                    this.hideModal(visibleModal);
                }
            }
        });
    }

    /**
     * 显示模态框
     */
    showModal(modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 隐藏模态框
     */
    hideModal(modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        // 如果是动态创建的模态框，延迟删除
        if (modal.dataset.dynamic === 'true') {
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * 初始化字符计数器
     */
    initCharacterCounters() {
        const fields = [
            { input: 'title', counter: 'titleCounter', max: 100 },
            { input: 'description', counter: 'descriptionCounter', max: 2000 },
            { input: 'specialDeadlineNote', counter: 'specialDeadlineCounter', max: 200 },
            { input: 'finishNote', counter: 'finishNoteCounter', max: 500 },
            { input: 'detailAddress', counter: 'detailAddressCounter', max: 200 }
        ];

        fields.forEach(field => {
            const input = document.getElementById(field.input);
            const counter = document.getElementById(field.counter);
            
            if (input && counter) {
                const updateCounter = () => {
                    const length = input.value.length;
                    counter.textContent = length;
                    counter.parentElement.classList.toggle('over-limit', length > field.max);
                };

                input.addEventListener('input', updateCounter);
                updateCounter(); // 初始化
            }
        });
    }

    /**
     * 初始化复制按钮
     */
    initCopyButtons() {
        document.addEventListener('click', (e) => {
            const copyBtn = e.target.closest('.copy-btn');
            if (copyBtn) {
                const targetId = copyBtn.dataset.target;
                if (targetId) {
                    this.copyToClipboard(targetId);
                }
            }
        });
    }

    /**
     * 复制到剪贴板
     */
    async copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        const copyBtn = document.querySelector(`[data-target="${elementId}"]`);

        if (!element) return;

        try {
            await navigator.clipboard.writeText(element.value);
            this.showCopySuccess(copyBtn);
            this.showMessage('复制成功', 'success');
        } catch (error) {
            // 降级方案
            element.select();
            document.execCommand('copy');
            this.showCopySuccess(copyBtn);
            this.showMessage('复制成功', 'success');
        }
    }

    /**
     * 显示复制成功状态
     */
    showCopySuccess(copyBtn) {
        if (!copyBtn) return;

        const originalIcon = copyBtn.querySelector('.btn-icon').textContent;
        const originalText = copyBtn.querySelector('.btn-text').textContent;

        // 添加成功状态
        copyBtn.classList.add('success');
        copyBtn.querySelector('.btn-icon').textContent = '✓';
        copyBtn.querySelector('.btn-text').textContent = '已复制';

        // 2秒后恢复原状
        setTimeout(() => {
            copyBtn.classList.remove('success');
            copyBtn.querySelector('.btn-icon').textContent = originalIcon;
            copyBtn.querySelector('.btn-text').textContent = originalText;
        }, 2000);
    }

    /**
     * 初始化表单操作
     */
    initFormActions() {
        // 这些操作将在form-handler.js中实现
        // 这里只是预留接口
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;
        
        // 添加样式
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '4px',
            color: 'white',
            fontSize: '14px',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        // 设置背景色
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#faad14',
            info: '#1890ff'
        };
        messageEl.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(messageEl);

        // 显示动画
        setTimeout(() => {
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                messageEl.remove();
            }, 300);
        }, 3000);
    }
}

// 创建全局UI组件实例
const uiComponents = new UIComponents();

// 导出模块（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UIComponents, uiComponents };
}
