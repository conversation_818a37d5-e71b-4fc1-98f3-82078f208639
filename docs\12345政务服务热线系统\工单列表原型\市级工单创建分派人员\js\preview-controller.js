/**
 * 快速预览面板控制器
 * 负责预览面板的显示、隐藏和数据渲染
 */

class PreviewController {
    constructor() {
        this.panel = document.getElementById('previewPanel');
        this.overlay = document.getElementById('previewOverlay');
        this.closeBtn = document.getElementById('previewCloseBtn');
        this.currentTicketId = null;
        
        this.initEventListeners();
    }
    
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 关闭按钮事件
        this.closeBtn.addEventListener('click', () => this.hide());
        
        // 遮罩层点击关闭
        this.overlay.addEventListener('click', () => this.hide());
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.panel.style.display !== 'none') {
                this.hide();
            }
        });
        
        // 复制按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-copy')) {
                this.handleCopyPhone(e);
            }
        });
        
        // 查看历史按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('#viewHistoryBtn')) {
                this.handleViewHistory();
            }
        });
        
        // 地图点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.map-placeholder')) {
                this.handleMapClick();
            }
        });
        
        // 附件点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.attachment-item')) {
                this.handleAttachmentClick(e);
            }
        });
        
        // 操作按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.preview-actions .btn')) {
                this.handleActionClick(e);
            }
        });
    }
    
    /**
     * 显示预览面板
     * @param {string} ticketId - 工单ID
     */
    show(ticketId) {
        const detailedData = getDetailedTicketData(ticketId);
        if (!detailedData) {
            console.warn('未找到工单详细数据，使用基础数据:', ticketId);
            this.showWithBasicData(ticketId);
            return;
        }

        this.currentTicketId = ticketId;
        this.renderPreviewData(detailedData);
        
        // 显示面板
        this.panel.style.display = 'flex';
        
        // 添加动画效果
        setTimeout(() => {
            this.panel.classList.add('show');
        }, 10);

        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
    }

    /**
     * 使用基础数据显示预览面板
     * @param {string} ticketId - 工单ID
     */
    showWithBasicData(ticketId) {
        // 从全局工单数据中查找基础信息
        const basicTicket = window.ticketController?.displayTickets?.find(t => t.id === ticketId) ||
                           window.ticketController?.filteredTickets?.find(t => t.id === ticketId);

        if (!basicTicket) {
            console.error('未找到基础工单数据:', ticketId);
            return;
        }

        // 构造简化的详细数据
        const simplifiedData = {
            woId: basicTicket.woId,
            title: basicTicket.title,
            content: basicTicket.content || '暂无详细描述',
            citizenName: basicTicket.citizenName || '未知',
            citizenPhone: '***',
            citizenAddress: '暂无地址信息',
            locationCoords: null,
            historicalWoCount: 0,
            historicalSatisfaction: 0,
            type: basicTicket.type,
            businessCategory: '暂无分类信息',
            priority: basicTicket.priority,
            channel: basicTicket.channel,
            status: basicTicket.status,
            attachments: [],
            returnReasons: [],
            flowHistory: [{
                step: '工单创建',
                operator: basicTicket.creatorName || '系统',
                time: basicTicket.createTime,
                status: '已完成',
                remark: '工单已创建'
            }]
        };

        this.currentTicketId = ticketId;
        this.renderPreviewData(simplifiedData);

        // 显示面板
        this.panel.style.display = 'flex';

        // 添加动画效果
        setTimeout(() => {
            this.panel.classList.add('show');
        }, 10);

        // 阻止页面滚动
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * 隐藏预览面板
     */
    hide() {
        this.panel.classList.remove('show');
        
        setTimeout(() => {
            this.panel.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
        
        this.currentTicketId = null;
    }
    
    /**
     * 渲染预览数据
     * @param {Object} data - 详细工单数据
     */
    renderPreviewData(data) {
        // 渲染标题栏
        this.renderHeader(data);
        
        // 渲染操作按钮
        this.renderActions(data);
        
        // 渲染内容区域
        this.renderContent(data);
        
        // 渲染市民信息
        this.renderCitizenInfo(data);
        
        // 渲染分类信息
        this.renderClassification(data);
        
        // 渲染历史追溯
        this.renderHistory(data);
    }
    
    /**
     * 渲染标题栏
     */
    renderHeader(data) {
        document.getElementById('previewTicketId').textContent = data.woId;
        document.getElementById('previewTicketTitle').textContent = data.title;
        
        // 渲染标签
        const tagsContainer = document.getElementById('previewTags');
        const tags = generateTicketTags(data);
        
        tagsContainer.innerHTML = tags.map(tag => `
            <span class="preview-tag ${tag.type}">
                <i class="${tag.icon}"></i>
                ${tag.text}
            </span>
        `).join('');
    }
    
    /**
     * 渲染操作按钮
     */
    renderActions(data) {
        const actionsContainer = document.getElementById('previewActions');
        const buttons = generateActionButtons(data.status, data.returnCount);
        
        actionsContainer.innerHTML = buttons.map(btn => {
            if (btn.dropdown) {
                return `
                    <div class="btn-group">
                        <button class="btn ${btn.type}" data-action="${btn.action}">
                            <i class="${btn.icon}"></i>
                            ${btn.text}
                        </button>
                    </div>
                `;
            } else {
                return `
                    <button class="btn btn-${btn.type}" data-action="${btn.action}">
                        <i class="${btn.icon}"></i>
                        ${btn.text}
                    </button>
                `;
            }
        }).join('');
    }
    
    /**
     * 渲染内容区域
     */
    renderContent(data) {
        document.getElementById('previewContent').textContent = data.content;
        
        // 渲染附件
        const attachmentsContainer = document.getElementById('previewAttachments');
        if (data.attachments && data.attachments.length > 0) {
            attachmentsContainer.innerHTML = data.attachments.map(att => `
                <div class="attachment-item" data-attachment-id="${att.id}">
                    <i class="fas fa-${att.type === 'image' ? 'image' : 'video'}"></i>
                    <span>${att.name}</span>
                    <small>(${att.size})</small>
                </div>
            `).join('');
        } else {
            attachmentsContainer.innerHTML = '<span class="text-muted">无附件</span>';
        }
    }
    
    /**
     * 渲染市民信息
     */
    renderCitizenInfo(data) {
        document.getElementById('previewCitizenName').textContent = data.citizenName;
        document.getElementById('previewCitizenPhone').textContent = data.citizenPhone;
        document.getElementById('previewHistoryCount').textContent = data.historicalWoCount;
        document.getElementById('previewSatisfaction').textContent = data.historicalSatisfaction + '%';
        document.getElementById('previewAddress').textContent = data.citizenAddress;
    }
    
    /**
     * 渲染分类信息
     */
    renderClassification(data) {
        const typeMap = {
            'complaint': '投诉',
            'consultation': '咨询',
            'suggestion': '建议',
            'help': '求助'
        };

        const priorityMap = {
            'urgent': '紧急',
            'high': '重要',
            'normal': '一般',
            'low': '较低'
        };

        const channelMap = {
            'phone': '电话',
            'website': '网站',
            'app': 'APP',
            'wechat': '微信',
            'email': '邮件'
        };
        
        document.getElementById('previewType').textContent = typeMap[data.type] || data.type;
        document.getElementById('previewCategory').textContent = data.businessCategory;
        document.getElementById('previewPriority').textContent = priorityMap[data.priority] || data.priority;
        document.getElementById('previewChannel').textContent = channelMap[data.channel] || data.channel;
    }
    
    /**
     * 渲染历史追溯
     */
    renderHistory(data) {
        // 渲染退回原因
        const returnReasonContainer = document.getElementById('previewReturnReason');
        if (data.return_reason && data.return_reason.length > 0) {
            returnReasonContainer.style.display = 'block';
            // 如果有多个退回原因，显示最新的一个
            const latestReturn = data.return_reason[data.return_reason.length - 1];
            returnReasonContainer.querySelector('.return-content').innerHTML = `
                <strong>[${latestReturn.unit}] 退回：</strong> ${latestReturn.reason}
                <div class="return-time">退回时间：${latestReturn.time}</div>
            `;
        } else {
            returnReasonContainer.style.display = 'none';
        }

        // 渲染流转历史
        const historyContainer = document.getElementById('previewFlowHistory');
        const flowHistory = data.flow_history_filtered || [];
        historyContainer.innerHTML = flowHistory.map(item => `
            <div class="flow-item">
                <span class="flow-time">${item.time}</span>
                <span class="flow-content">
                    <span class="flow-action">${item.action}</span> - ${item.description}
                </span>
            </div>
        `).join('');
    }
    
    /**
     * 处理复制电话号码
     */
    handleCopyPhone(e) {
        const phoneElement = e.target.closest('.info-item').querySelector('#previewCitizenPhone');
        const phone = phoneElement.textContent;
        
        navigator.clipboard.writeText(phone).then(() => {
            // 显示复制成功提示
            this.showToast('电话号码已复制到剪贴板');
        }).catch(() => {
            this.showToast('复制失败，请手动复制');
        });
    }
    
    /**
     * 处理查看历史
     */
    handleViewHistory() {
        this.showToast('查看历史功能开发中...');
    }
    
    /**
     * 处理地图点击
     */
    handleMapClick() {
        this.showToast('地图功能开发中...');
    }
    
    /**
     * 处理附件点击
     */
    handleAttachmentClick(e) {
        const attachmentId = e.target.closest('.attachment-item').dataset.attachmentId;
        this.showToast(`预览附件: ${attachmentId}`);
    }
    
    /**
     * 处理操作按钮点击
     */
    handleActionClick(e) {
        const action = e.target.closest('.btn').dataset.action;
        this.showToast(`执行操作: ${action}`);
    }
    
    /**
     * 显示提示消息
     */
    showToast(message) {
        // 简单的提示实现
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => toast.style.opacity = '1', 10);
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }
}

// 全局预览控制器实例
let previewController;
