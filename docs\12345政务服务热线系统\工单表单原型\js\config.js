/**
 * 配置和常量
 * 包含系统配置、常量定义、默认值等
 */

// 系统配置
const CONFIG = {
    // 表单配置
    form: {
        autoSaveInterval: 30000, // 自动保存间隔（毫秒）
        maxFileSize: 200 * 1024 * 1024, // 最大文件总大小（200MB）
        maxFileCount: 10, // 最大文件数量
        maxCustomTags: 3, // 最大自定义标签数量
        maxBusinessTags: 5, // 最大业务标签数量
    },
    
    // 文件上传配置
    upload: {
        allowedTypes: {
            image: {
                extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
                maxSize: 10 * 1024 * 1024, // 10MB
                mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp']
            },
            document: {
                extensions: ['pdf', 'doc', 'docx', 'txt'],
                maxSize: 20 * 1024 * 1024, // 20MB
                mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
            },
            audio: {
                extensions: ['mp3', 'wav', 'm4a'],
                maxSize: 50 * 1024 * 1024, // 50MB
                mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/mp4']
            },
            video: {
                extensions: ['mp4', 'avi', 'mov'],
                maxSize: 100 * 1024 * 1024, // 100MB
                mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime']
            }
        }
    },
    
    // 验证配置
    validation: {
        phone: /^1[3-9]\d{9}$/, // 手机号正则
        landline: /^0\d{2,3}-?\d{7,8}$/, // 座机号正则
        idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, // 身份证正则
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // 邮箱正则
        zipCode: /^\d{6}$/, // 邮编正则
        chineseName: /^[\u4e00-\u9fa5]{2,20}$/, // 中文姓名正则
    },
    
    // API配置（模拟）
    api: {
        baseUrl: '/api/v1',
        timeout: 30000,
        endpoints: {
            saveDraft: '/tickets/draft',
            submitTicket: '/tickets/submit',
            uploadFile: '/files/upload',
            getDepartments: '/departments',
            getCategories: '/categories',
            validateAddress: '/address/validate'
        }
    }
};

// 常量定义
const CONSTANTS = {
    // 工单类型
    TICKET_TYPES: [
        { value: '投诉', label: '投诉', icon: '📢' },
        { value: '建议', label: '建议', icon: '💡' },
        { value: '咨询', label: '咨询', icon: '❓' },
        { value: '求助', label: '求助', icon: '🆘' },
        { value: '表扬', label: '表扬', icon: '👍' },
        { value: '举报', label: '举报', icon: '🚨' }
    ],
    
    // 紧急程度
    PRIORITY_LEVELS: [
        { value: '普通', label: '普通', color: 'var(--priority-normal)' },
        { value: '紧急', label: '紧急', color: 'var(--priority-urgent)' },
        { value: '特急', label: '特急', color: 'var(--priority-critical)' }
    ],
    
    // 工单来源
    TICKET_SOURCES: [
        '电话来电', '网站留言', '手机APP', '微信公众号', 
        '现场接待', '领导批示', '媒体曝光', '其他'
    ],
    
    // 处理模式
    PROCESS_MODES: [
        { value: '即时办结', label: '即时办结', desc: '适用于简单咨询类问题' },
        { value: '派单处理', label: '派单处理', desc: '需要指定承办单位' }
    ],
    
    // 客户类型
    CITIZEN_TYPES: [
        { value: '个人', label: '个人' },
        { value: '企业', label: '企业' },
        { value: '社会组织', label: '社会组织' },
        { value: '政府机关', label: '政府机关' }
    ],
    
    // 特殊标识
    SPECIAL_IDENTITIES: [
        '人大代表', '政协委员', '劳动模范', '退役军人', 
        '残疾人', '低保户', '其他'
    ],
    
    // 业务标签
    BUSINESS_TAGS: [
        '重点关注', '重复投诉', '媒体关注', '领导批示', 
        '紧急处理', '跨区域', '多部门', '历史遗留', 
        '政策咨询', '便民服务', '其他'
    ],
    
    // 默认时限设置（工作日）
    DEFAULT_DEADLINES: {
        accept: {
            '投诉': 1,
            '建议': 2,
            '咨询': 0.5,
            '求助': 1,
            '表扬': 1,
            '举报': 1
        },
        finish: {
            '投诉': 15,
            '建议': 30,
            '咨询': 3,
            '求助': 10,
            '表扬': 5,
            '举报': 20
        }
    },
    
    // 表单状态
    FORM_STATUS: {
        DRAFT: 'draft',
        SUBMITTED: 'submitted',
        PROCESSING: 'processing',
        COMPLETED: 'completed',
        REJECTED: 'rejected'
    },
    
    // 消息类型
    MESSAGE_TYPES: {
        SUCCESS: 'success',
        ERROR: 'error',
        WARNING: 'warning',
        INFO: 'info'
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        DRAFT_DATA: 'ticket_draft_data',
        USER_PREFERENCES: 'user_preferences',
        RECENT_ADDRESSES: 'recent_addresses',
        CUSTOM_TAGS: 'custom_tags'
    }
};

// 工具函数
const UTILS = {
    /**
     * 生成工单编号
     * @returns {string} 工单编号
     */
    generateTicketNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const serial = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
        return `WD${year}${month}${day}${serial}`;
    },
    
    /**
     * 格式化时间
     * @param {Date} date 日期对象
     * @returns {string} 格式化后的时间字符串
     */
    formatDateTime(date = new Date()) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 获取文件类型
     * @param {string} filename 文件名
     * @returns {string} 文件类型
     */
    getFileType(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        for (const [type, config] of Object.entries(CONFIG.upload.allowedTypes)) {
            if (config.extensions.includes(ext)) {
                return type;
            }
        }
        return 'unknown';
    },
    
    /**
     * 验证文件
     * @param {File} file 文件对象
     * @returns {Object} 验证结果
     */
    validateFile(file) {
        const fileType = this.getFileType(file.name);
        
        if (fileType === 'unknown') {
            return { valid: false, message: '不支持的文件格式' };
        }
        
        const config = CONFIG.upload.allowedTypes[fileType];
        if (file.size > config.maxSize) {
            return { 
                valid: false, 
                message: `文件大小超过限制（最大${this.formatFileSize(config.maxSize)}）` 
            };
        }
        
        return { valid: true, type: fileType };
    },
    
    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 深拷贝对象
     * @param {Object} obj 要拷贝的对象
     * @returns {Object} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * 本地存储操作
     */
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('存储数据失败:', e);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取数据失败:', e);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('删除数据失败:', e);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('清空数据失败:', e);
                return false;
            }
        }
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, CONSTANTS, UTILS };
}
