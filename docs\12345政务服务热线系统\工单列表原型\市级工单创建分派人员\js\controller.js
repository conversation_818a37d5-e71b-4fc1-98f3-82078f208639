/**
 * 工单列表控制器
 * 负责页面逻辑控制、数据管理、用户交互等
 */

class TicketListController {
    constructor() {
        this.currentFilter = 'myDrafts';
        this.currentPage = 1;
        this.pageSize = CONFIG.pagination.defaultPageSize;
        this.sortField = CONFIG.table.defaultSortField;
        this.sortOrder = CONFIG.table.defaultSortOrder;
        this.searchKeyword = '';
        this.advancedSearchParams = {};
        this.selectedTickets = new Set();
        this.filteredTickets = [];
        this.displayTickets = [];
        
        this.init();
    }
    
    /**
     * 初始化控制器
     */
    init() {
        this.allTickets = []; // 初始化为空数组
        this.bindEvents();
        this.loadData();
        this.updateStatistics();
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 筛选标签点击
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const filter = e.currentTarget.dataset.filter;
                this.applyFilter(filter);
            });
        });
        
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        searchInput.addEventListener('input', this.debounce((e) => {
            this.searchKeyword = e.target.value.trim();
            this.performSearch();
        }, CONFIG.filter.debounceDelay));
        
        searchBtn.addEventListener('click', () => {
            this.performSearch();
        });
        
        // 高级搜索
        document.getElementById('advancedSearchBtn').addEventListener('click', () => {
            this.toggleAdvancedSearch();
        });
        
        document.getElementById('doAdvancedSearch').addEventListener('click', () => {
            this.performAdvancedSearch();
        });
        
        document.getElementById('resetSearch').addEventListener('click', () => {
            this.resetSearch();
        });
        
        document.getElementById('collapseSearch').addEventListener('click', () => {
            this.toggleAdvancedSearch(false);
        });
        
        // 表格排序
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const field = e.currentTarget.dataset.sort;
                this.applySorting(field);
            });
        });
        
        // 全选功能
        document.getElementById('selectAll').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // 分页功能
        document.getElementById('pageSizeSelect').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.renderTable();
            this.renderPagination();
        });
        
        // 批量操作
        document.getElementById('batchAssignBtn').addEventListener('click', () => {
            this.showBatchAssignModal();
        });
        
        document.getElementById('batchCloseBtn').addEventListener('click', () => {
            this.showBatchCloseModal();
        });
        
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportTickets();
        });
        
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshData();
        });
        
        // 新建工单
        document.getElementById('createTicketBtn').addEventListener('click', () => {
            this.createNewTicket();
        });
    }
    
    /**
     * 加载数据
     */
    loadData() {
        this.showLoading(true);

        // 模拟API调用
        setTimeout(() => {
            this.allTickets = [...window.MockData.tickets];
            this.showLoading(false);
            this.applyFilter(this.currentFilter);
        }, 500);
    }
    
    /**
     * 刷新数据
     */
    refreshData() {
        this.loadData();
        this.updateStatistics();
        this.showMessage('数据已刷新', 'success');
    }
    
    /**
     * 更新统计数据
     */
    updateStatistics() {
        const stats = window.MockData.statistics;
        
        document.getElementById('todayCount').textContent = stats.todayCount;
        document.getElementById('pendingCount').textContent = stats.pendingCount;
        
        // 更新标签页计数
        document.getElementById('myDraftsCount').textContent = stats.myDraftsCount;
        document.getElementById('returnedCount').textContent = stats.returnedCount;
        document.getElementById('assignedCount').textContent = stats.assignedCount;
        document.getElementById('closedCount').textContent = stats.closedCount;
        document.getElementById('todayTabCount').textContent = stats.todayCount;
        document.getElementById('urgentCount').textContent = stats.urgentCount;
        document.getElementById('allCount').textContent = stats.allCount;
    }
    
    /**
     * 应用筛选器
     */
    applyFilter(filterType) {
        this.currentFilter = filterType;
        this.currentPage = 1;
        this.selectedTickets.clear();
        
        // 更新标签页状态
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');
        
        // 应用筛选条件
        const filterConfig = FILTER_CONFIG[filterType];
        if (filterConfig) {
            this.filteredTickets = this.allTickets.filter(ticket => 
                filterConfig.condition(ticket, CURRENT_USER)
            );
        } else {
            this.filteredTickets = [...this.allTickets];
        }
        
        this.applySorting(this.sortField, this.sortOrder);
        this.renderTable();
        this.renderPagination();
        this.updateBatchActions();
    }
    
    /**
     * 执行搜索
     */
    performSearch() {
        if (!this.searchKeyword) {
            this.applyFilter(this.currentFilter);
            return;
        }
        
        const keyword = this.searchKeyword.toLowerCase();
        this.filteredTickets = this.filteredTickets.filter(ticket => {
            return ticket.woId.toLowerCase().includes(keyword) ||
                   ticket.title.toLowerCase().includes(keyword) ||
                   ticket.citizenName.toLowerCase().includes(keyword) ||
                   ticket.citizenPhone.includes(keyword);
        });
        
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }
    
    /**
     * 执行高级搜索
     */
    performAdvancedSearch() {
        const params = this.getAdvancedSearchParams();
        this.advancedSearchParams = params;
        
        let results = [...this.allTickets];
        
        // 应用各种搜索条件
        if (params.ticketId) {
            results = results.filter(ticket => 
                ticket.woId.toLowerCase().includes(params.ticketId.toLowerCase())
            );
        }
        
        if (params.title) {
            results = results.filter(ticket => 
                ticket.title.toLowerCase().includes(params.title.toLowerCase())
            );
        }
        
        if (params.citizenName) {
            results = results.filter(ticket => 
                ticket.citizenName.toLowerCase().includes(params.citizenName.toLowerCase())
            );
        }
        
        if (params.phone) {
            results = results.filter(ticket => 
                ticket.citizenPhone.includes(params.phone)
            );
        }
        
        if (params.status) {
            results = results.filter(ticket => ticket.status === params.status);
        }
        
        if (params.type) {
            results = results.filter(ticket => ticket.type === params.type);
        }
        
        if (params.priority) {
            results = results.filter(ticket => ticket.priority === params.priority);
        }
        
        if (params.channel) {
            results = results.filter(ticket => ticket.channel === params.channel);
        }
        
        if (params.unit) {
            results = results.filter(ticket => ticket.responsibleUnit === params.unit);
        }
        
        if (params.creator) {
            results = results.filter(ticket => 
                ticket.creatorName.toLowerCase().includes(params.creator.toLowerCase())
            );
        }
        
        if (params.startDate) {
            const startDate = new Date(params.startDate);
            results = results.filter(ticket => 
                new Date(ticket.createTime) >= startDate
            );
        }
        
        if (params.endDate) {
            const endDate = new Date(params.endDate);
            endDate.setHours(23, 59, 59, 999);
            results = results.filter(ticket => 
                new Date(ticket.createTime) <= endDate
            );
        }
        
        this.filteredTickets = results;
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
        
        this.showMessage(`找到 ${results.length} 条匹配记录`, 'info');
    }
    
    /**
     * 获取高级搜索参数
     */
    getAdvancedSearchParams() {
        return {
            ticketId: document.getElementById('searchTicketId').value.trim(),
            title: document.getElementById('searchTitle').value.trim(),
            citizenName: document.getElementById('searchCitizenName').value.trim(),
            phone: document.getElementById('searchPhone').value.trim(),
            status: document.getElementById('searchStatus').value,
            type: document.getElementById('searchType').value,
            priority: document.getElementById('searchPriority').value,
            channel: document.getElementById('searchChannel').value,
            unit: document.getElementById('searchUnit').value,
            creator: document.getElementById('searchCreator').value.trim(),
            startDate: document.getElementById('searchStartDate').value,
            endDate: document.getElementById('searchEndDate').value
        };
    }
    
    /**
     * 重置搜索
     */
    resetSearch() {
        // 清空基础搜索
        document.getElementById('searchInput').value = '';
        this.searchKeyword = '';
        
        // 清空高级搜索
        document.querySelectorAll('#advancedSearchPanel input, #advancedSearchPanel select').forEach(input => {
            input.value = '';
        });
        this.advancedSearchParams = {};
        
        // 重新应用当前筛选器
        this.applyFilter(this.currentFilter);
    }
    
    /**
     * 切换高级搜索面板
     */
    toggleAdvancedSearch(show = null) {
        const panel = document.getElementById('advancedSearchPanel');
        const isVisible = panel.style.display !== 'none';
        
        if (show === null) {
            show = !isVisible;
        }
        
        panel.style.display = show ? 'block' : 'none';
        
        const btn = document.getElementById('advancedSearchBtn');
        btn.classList.toggle('active', show);
    }
    
    /**
     * 应用排序
     */
    applySorting(field, order = null) {
        if (field === this.sortField) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = order || 'desc';
        }
        
        // 更新表头排序指示器
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('asc', 'desc');
        });
        
        const currentHeader = document.querySelector(`[data-sort="${field}"]`);
        if (currentHeader) {
            currentHeader.classList.add(this.sortOrder);
        }
        
        // 执行排序
        this.filteredTickets.sort((a, b) => {
            let valueA = a[field];
            let valueB = b[field];
            
            // 处理不同数据类型的排序
            if (field === 'createTime') {
                valueA = new Date(valueA);
                valueB = new Date(valueB);
            } else if (field === 'priority') {
                valueA = PRIORITY_CONFIG[valueA]?.weight || 0;
                valueB = PRIORITY_CONFIG[valueB]?.weight || 0;
            } else if (typeof valueA === 'string') {
                valueA = valueA.toLowerCase();
                valueB = valueB.toLowerCase();
            }
            
            if (valueA < valueB) {
                return this.sortOrder === 'asc' ? -1 : 1;
            }
            if (valueA > valueB) {
                return this.sortOrder === 'asc' ? 1 : -1;
            }
            return 0;
        });
        
        this.renderTable();
    }
    
    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 显示加载状态
     */
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }
    
    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 这里可以实现消息提示组件
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 简单的消息提示实现
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${MESSAGE_CONFIG[type].color};
            color: white;
            border-radius: 6px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;

        document.body.appendChild(messageEl);

        setTimeout(() => {
            messageEl.remove();
        }, MESSAGE_CONFIG[type].duration);
    }

    /**
     * 渲染表格
     */
    renderTable() {
        const tbody = document.getElementById('ticketTableBody');
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        this.displayTickets = this.filteredTickets.slice(startIndex, endIndex);

        if (this.displayTickets.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="13" class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>暂无数据</h3>
                        <p>当前筛选条件下没有找到工单记录</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.displayTickets.map(ticket => this.renderTicketRow(ticket)).join('');

        // 绑定行级事件
        this.bindRowEvents();

        // 更新总记录数
        document.getElementById('totalCount').textContent = this.filteredTickets.length;
    }

    /**
     * 渲染工单行
     */
    renderTicketRow(ticket) {
        const statusConfig = STATUS_CONFIG[ticket.status];
        const priorityConfig = PRIORITY_CONFIG[ticket.priority];
        const typeConfig = TYPE_CONFIG[ticket.type];
        const channelConfig = CHANNEL_CONFIG[ticket.channel];

        return `
            <tr data-ticket-id="${ticket.id}" ${this.selectedTickets.has(ticket.id) ? 'class="selected"' : ''}>
                <td class="checkbox-col">
                    <input type="checkbox" class="row-checkbox" ${this.selectedTickets.has(ticket.id) ? 'checked' : ''}>
                </td>
                <td>
                    <span class="ticket-id">${ticket.woId}</span>
                </td>
                <td>
                    <div class="ticket-title" title="${ticket.title}">
                        ${this.truncateText(ticket.title, 30)}
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusConfig.class}">
                        <i class="${statusConfig.icon}"></i>
                        ${statusConfig.label}
                    </span>
                </td>
                <td>
                    <span class="priority-badge ${priorityConfig.class}">
                        <i class="${priorityConfig.icon}"></i>
                        ${priorityConfig.label}
                    </span>
                </td>
                <td>
                    <span class="channel-badge">
                        <i class="${channelConfig.icon}"></i>
                        ${channelConfig.label}
                    </span>
                </td>
                <td>
                    <span class="type-badge">
                        <i class="${typeConfig.icon}"></i>
                        ${typeConfig.label}
                    </span>
                </td>
                <td>${ticket.citizenName}</td>
                <td>${ticket.creatorName}</td>
                <td>${this.formatDateTime(ticket.createTime)}</td>
                <td>${ticket.responsibleUnit ? RESPONSIBLE_UNITS[ticket.responsibleUnit] : '-'}</td>
                <td>${this.renderRemainingTime(ticket.remainingTime)}</td>
                <td class="actions-col">
                    ${this.renderActionButtons(ticket)}
                </td>
            </tr>
        `;
    }

    /**
     * 渲染剩余时间
     */
    renderRemainingTime(remainingTime) {
        if (!remainingTime) {
            return '-';
        }

        const { hours, minutes, percentage, status } = remainingTime;

        if (status === 'overdue') {
            return `
                <div class="time-progress">
                    <div class="time-text" style="color: #e74c3c;">已超时</div>
                    <div class="progress-bar">
                        <div class="progress-fill danger" style="width: 100%;"></div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="time-progress">
                <div class="time-text">${hours}h ${minutes}m</div>
                <div class="progress-bar">
                    <div class="progress-fill ${status}" style="width: ${percentage}%;"></div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染操作按钮
     */
    renderActionButtons(ticket) {
        const permissions = CURRENT_USER.permissions.rowActions;
        const buttons = [];

        // 查看详情
        if (permissions.view) {
            buttons.push(`<button class="btn btn-xs btn-outline view-btn" data-action="view" title="查看详情">
                <i class="fas fa-eye"></i>
            </button>`);
        }

        // 编辑
        if (typeof permissions.edit === 'function' ? permissions.edit(ticket, CURRENT_USER) : permissions.edit) {
            buttons.push(`<button class="btn btn-xs btn-primary edit-btn" data-action="edit" title="编辑工单">
                <i class="fas fa-edit"></i>
            </button>`);
        }

        // 指派
        if (typeof permissions.assign === 'function' ? permissions.assign(ticket, CURRENT_USER) : permissions.assign) {
            buttons.push(`<button class="btn btn-xs btn-success assign-btn" data-action="assign" title="指派工单">
                <i class="fas fa-share"></i>
            </button>`);
        }

        // 即时办结
        if (typeof permissions.close === 'function' ? permissions.close(ticket, CURRENT_USER) : permissions.close) {
            buttons.push(`<button class="btn btn-xs btn-warning close-btn" data-action="close" title="即时办结">
                <i class="fas fa-check"></i>
            </button>`);
        }

        // 更多操作下拉菜单
        const moreActions = [];

        if (typeof permissions.recall === 'function' ? permissions.recall(ticket, CURRENT_USER) : permissions.recall) {
            moreActions.push(`<a class="dropdown-item" data-action="recall">
                <i class="fas fa-undo"></i> 撤回工单
            </a>`);
        }

        if (typeof permissions.urge === 'function' ? permissions.urge(ticket, CURRENT_USER) : permissions.urge) {
            moreActions.push(`<a class="dropdown-item" data-action="urge">
                <i class="fas fa-bell"></i> 催办工单
            </a>`);
        }

        if (permissions.copy) {
            moreActions.push(`<a class="dropdown-item" data-action="copy">
                <i class="fas fa-copy"></i> 复制工单
            </a>`);
        }

        if (moreActions.length > 0) {
            buttons.push(`
                <div class="dropdown">
                    <button class="btn btn-xs btn-outline more-btn" title="更多操作">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-menu">
                        ${moreActions.join('')}
                    </div>
                </div>
            `);
        }

        return `<div class="action-buttons">${buttons.join('')}</div>`;
    }

    /**
     * 绑定行级事件
     */
    bindRowEvents() {
        // 行点击事件 - 触发快速预览
        document.querySelectorAll('#ticketTableBody tr').forEach(row => {
            row.addEventListener('click', (e) => {
                // 如果点击的是复选框、按钮或下拉菜单，不触发预览
                if (e.target.closest('input[type="checkbox"]') ||
                    e.target.closest('button') ||
                    e.target.closest('.dropdown')) {
                    return;
                }

                const ticketId = row.dataset.ticketId;
                if (ticketId && previewController) {
                    previewController.show(ticketId);
                }
            });

            // 添加悬停效果
            row.addEventListener('mouseenter', () => {
                if (!row.classList.contains('selected')) {
                    row.style.backgroundColor = 'var(--gray-50)';
                }
            });

            row.addEventListener('mouseleave', () => {
                if (!row.classList.contains('selected')) {
                    row.style.backgroundColor = '';
                }
            });
        });

        // 复选框事件
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const row = e.target.closest('tr');
                const ticketId = row.dataset.ticketId;

                if (e.target.checked) {
                    this.selectedTickets.add(ticketId);
                    row.classList.add('selected');
                } else {
                    this.selectedTickets.delete(ticketId);
                    row.classList.remove('selected');
                }

                this.updateBatchActions();
                this.updateSelectAllState();
            });
        });

        // 操作按钮事件
        document.querySelectorAll('.action-buttons button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = e.currentTarget.dataset.action;
                const row = e.currentTarget.closest('tr');
                const ticketId = row.dataset.ticketId;
                const ticket = this.displayTickets.find(t => t.id === ticketId);

                this.handleRowAction(action, ticket);
            });
        });

        // 下拉菜单事件
        document.querySelectorAll('.more-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const dropdown = e.currentTarget.closest('.dropdown');
                dropdown.classList.toggle('active');
            });
        });

        document.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = e.currentTarget.dataset.action;
                const row = e.currentTarget.closest('tr');
                const ticketId = row.dataset.ticketId;
                const ticket = this.displayTickets.find(t => t.id === ticketId);

                // 关闭下拉菜单
                e.currentTarget.closest('.dropdown').classList.remove('active');

                this.handleRowAction(action, ticket);
            });
        });

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', () => {
            document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        });
    }

    /**
     * 处理行级操作
     */
    handleRowAction(action, ticket) {
        switch (action) {
            case 'view':
                this.viewTicketDetail(ticket);
                break;
            case 'edit':
                this.editTicket(ticket);
                break;
            case 'assign':
                this.assignTicket(ticket);
                break;
            case 'close':
                this.closeTicket(ticket);
                break;
            case 'recall':
                this.recallTicket(ticket);
                break;
            case 'urge':
                this.urgeTicket(ticket);
                break;
            case 'copy':
                this.copyTicket(ticket);
                break;
            default:
                console.warn('未知操作:', action);
        }
    }

    /**
     * 查看工单详情
     */
    viewTicketDetail(ticket) {
        this.showMessage(`查看工单详情: ${ticket.woId}`, 'info');
        // 这里可以打开详情模态框或跳转到详情页面
    }

    /**
     * 编辑工单
     */
    editTicket(ticket) {
        this.showMessage(`编辑工单: ${ticket.woId}`, 'info');
        // 这里可以打开编辑模态框或跳转到编辑页面
    }

    /**
     * 指派工单
     */
    assignTicket(ticket) {
        this.showMessage(`指派工单: ${ticket.woId}`, 'info');
        // 这里可以打开指派模态框
    }

    /**
     * 即时办结工单
     */
    closeTicket(ticket) {
        if (confirm(`确定要即时办结工单 ${ticket.woId} 吗？`)) {
            // 模拟API调用
            this.showLoading(true);
            setTimeout(() => {
                ticket.status = TICKET_STATUS.CLOSED;
                ticket.closeType = 'immediate';
                this.showLoading(false);
                this.showMessage(`工单 ${ticket.woId} 已即时办结`, 'success');
                this.refreshData();
            }, 1000);
        }
    }

    /**
     * 撤回工单
     */
    recallTicket(ticket) {
        if (confirm(`确定要撤回工单 ${ticket.woId} 吗？`)) {
            // 模拟API调用
            this.showLoading(true);
            setTimeout(() => {
                ticket.status = TICKET_STATUS.DRAFT;
                ticket.responsibleUnit = null;
                this.showLoading(false);
                this.showMessage(`工单 ${ticket.woId} 已撤回`, 'success');
                this.refreshData();
            }, 1000);
        }
    }

    /**
     * 催办工单
     */
    urgeTicket(ticket) {
        this.showMessage(`已向 ${RESPONSIBLE_UNITS[ticket.responsibleUnit]} 发送催办通知`, 'success');
    }

    /**
     * 复制工单
     */
    copyTicket(ticket) {
        this.showMessage(`复制工单: ${ticket.woId}`, 'info');
        // 这里可以打开新建工单页面并预填数据
    }

    /**
     * 更新批量操作状态
     */
    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');

        if (this.selectedTickets.size > 0) {
            batchActions.style.display = 'flex';
            selectedCount.textContent = this.selectedTickets.size;
        } else {
            batchActions.style.display = 'none';
        }
    }

    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        const selectAll = document.getElementById('selectAll');
        const totalRows = this.displayTickets.length;
        const selectedRows = this.displayTickets.filter(ticket =>
            this.selectedTickets.has(ticket.id)
        ).length;

        if (selectedRows === 0) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        } else if (selectedRows === totalRows) {
            selectAll.checked = true;
            selectAll.indeterminate = false;
        } else {
            selectAll.checked = false;
            selectAll.indeterminate = true;
        }
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll(checked) {
        this.displayTickets.forEach(ticket => {
            if (checked) {
                this.selectedTickets.add(ticket.id);
            } else {
                this.selectedTickets.delete(ticket.id);
            }
        });

        // 更新表格行状态
        document.querySelectorAll('.row-checkbox').forEach((checkbox, index) => {
            checkbox.checked = checked;
            const row = checkbox.closest('tr');
            row.classList.toggle('selected', checked);
        });

        this.updateBatchActions();
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredTickets.length / this.pageSize);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        const buttons = [];

        // 上一页按钮
        buttons.push(`
            <button class="page-btn" ${this.currentPage === 1 ? 'disabled' : ''} data-page="${this.currentPage - 1}">
                <i class="fas fa-chevron-left"></i>
            </button>
        `);

        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            buttons.push(`<button class="page-btn" data-page="1">1</button>`);
            if (startPage > 2) {
                buttons.push(`<span class="page-ellipsis">...</span>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="page-btn ${i === this.currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                buttons.push(`<span class="page-ellipsis">...</span>`);
            }
            buttons.push(`<button class="page-btn" data-page="${totalPages}">${totalPages}</button>`);
        }

        // 下一页按钮
        buttons.push(`
            <button class="page-btn" ${this.currentPage === totalPages ? 'disabled' : ''} data-page="${this.currentPage + 1}">
                <i class="fas fa-chevron-right"></i>
            </button>
        `);

        pagination.innerHTML = buttons.join('');

        // 绑定分页事件
        pagination.querySelectorAll('.page-btn:not([disabled])').forEach(button => {
            button.addEventListener('click', (e) => {
                const page = parseInt(e.currentTarget.dataset.page);
                if (page && page !== this.currentPage) {
                    this.currentPage = page;
                    this.renderTable();
                    this.renderPagination();
                    this.selectedTickets.clear();
                    this.updateBatchActions();
                }
            });
        });
    }

    /**
     * 批量指派工单
     */
    showBatchAssignModal() {
        if (this.selectedTickets.size === 0) {
            this.showMessage('请先选择要指派的工单', 'warning');
            return;
        }

        this.showMessage(`批量指派 ${this.selectedTickets.size} 个工单`, 'info');
        // 这里可以打开批量指派模态框
    }

    /**
     * 批量办结工单
     */
    showBatchCloseModal() {
        if (this.selectedTickets.size === 0) {
            this.showMessage('请先选择要办结的工单', 'warning');
            return;
        }

        if (confirm(`确定要批量办结 ${this.selectedTickets.size} 个工单吗？`)) {
            this.showLoading(true);
            setTimeout(() => {
                // 模拟批量操作
                this.selectedTickets.forEach(ticketId => {
                    const ticket = this.allTickets.find(t => t.id === ticketId);
                    if (ticket && ticket.status === TICKET_STATUS.DRAFT) {
                        ticket.status = TICKET_STATUS.CLOSED;
                        ticket.closeType = 'immediate';
                    }
                });

                this.selectedTickets.clear();
                this.showLoading(false);
                this.showMessage('批量办结完成', 'success');
                this.refreshData();
            }, 2000);
        }
    }

    /**
     * 导出工单
     */
    exportTickets() {
        const tickets = this.selectedTickets.size > 0
            ? this.filteredTickets.filter(ticket => this.selectedTickets.has(ticket.id))
            : this.filteredTickets;

        if (tickets.length === 0) {
            this.showMessage('没有可导出的数据', 'warning');
            return;
        }

        this.showMessage(`正在导出 ${tickets.length} 条记录...`, 'info');

        // 模拟导出过程
        setTimeout(() => {
            this.showMessage('导出完成', 'success');
        }, 1500);
    }

    /**
     * 新建工单
     */
    createNewTicket() {
        this.showMessage('跳转到新建工单页面', 'info');
        // 这里可以跳转到新建工单页面
    }

    /**
     * 截断文本
     */
    truncateText(text, maxLength) {
        if (text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + '...';
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
    }

    /**
     * 销毁控制器
     */
    destroy() {
        // 清理事件监听器和定时器
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
        }

        // 清理其他资源
        this.selectedTickets.clear();
        this.filteredTickets = [];
        this.displayTickets = [];
    }
}
