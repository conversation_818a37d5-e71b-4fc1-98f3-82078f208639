/**
 * 预览面板详细数据文件
 * 包含工单的详细信息，用于快速预览面板显示
 */

// 详细工单数据映射
const DETAILED_TICKET_DATA = {
    // 示例工单1 - 噪音扰民投诉（被退回）
    'ticket001': {
        // 基础信息
        woId: '202507282305',
        title: '关于XX小区夜间施工噪音扰民的投诉',
        content: `市民反映，自本周一开始，位于XX区XX路的XX小区旁的建筑工地，每天晚上11点后依然进行土方作业，噪音巨大，严重影响了周边居民的正常休息。

具体情况：
1. 施工时间：每晚11:00-凌晨2:00
2. 噪音类型：挖掘机作业、渣土车进出
3. 影响范围：周边3个小区约500户居民
4. 持续时间：已持续一周

希望相关部门能够立即核实处理，制止违规夜间施工行为，还居民一个安静的休息环境。`,
        
        // 市民信息
        citizenName: '王**',
        citizenPhone: '138****1234',
        citizenAddress: 'XX区XX路XX小区3栋2单元501室',
        locationCoords: { lat: 39.9042, lng: 116.4074 }, // 示例坐标
        historicalWoCount: 3,
        historicalSatisfaction: 67,
        
        // 分类信息
        type: 'complaint',
        businessCategory: '城市管理 → 工地管理 → 工地噪音',
        priority: 'urgent',
        channel: 'phone',
        status: 'draft',
        
        // 附件信息
        attachments: [
            {
                id: 'att001',
                name: '现场照片.jpg',
                type: 'image',
                size: '2.3MB',
                url: '#',
                thumbnail: '#'
            },
            {
                id: 'att002', 
                name: '施工视频.mp4',
                type: 'video',
                size: '15.7MB',
                url: '#',
                thumbnail: '#'
            }
        ],
        
        // 退回信息
        returnReason: {
            unit: '区城管局',
            reason: '此事发地经核实属于XX街道管辖范围，非我局直管，请改派至XX街道办事处处理。',
            returnTime: '2025-07-28 14:30',
            returnBy: '李四'
        },
        
        // 流转历史
        flowHistory: [
            {
                time: '14:30',
                action: '退回',
                content: '[区城管局] 退回至 [市级中心]',
                operator: '李四'
            },
            {
                time: '10:35',
                action: '派发',
                content: '[市级中心] 派发至 [区城管局]',
                operator: '张三'
            },
            {
                time: '09:30',
                action: '创建',
                content: '[市级中心] 创建工单',
                operator: '张三'
            }
        ]
    },
    
    // 示例工单2 - 道路积水（处理中）
    'ticket002': {
        woId: '202507289296',
        title: '道路积水严重，影响通行',
        content: `下雨后道路积水严重，车辆和行人通行困难，请相关部门及时处理。

具体位置：XX区XX路与XX街交叉口
积水深度：约20-30厘米
影响情况：
- 车辆通行缓慢，容易熄火
- 行人需要绕行，影响出行
- 附近商铺进水风险较高

建议尽快疏通排水系统，清理堵塞物。`,
        
        citizenName: '李**',
        citizenPhone: '139****1234', 
        citizenAddress: 'XX区XX路XX号',
        locationCoords: { lat: 39.9142, lng: 116.4174 },
        historicalWoCount: 1,
        historicalSatisfaction: 85,
        
        type: 'complaint',
        businessCategory: '城市管理 → 市政设施 → 道路排水',
        priority: 'urgent',
        channel: 'app',
        status: 'processing',
        
        attachments: [
            {
                id: 'att003',
                name: '积水现场图.jpg',
                type: 'image', 
                size: '1.8MB',
                url: '#',
                thumbnail: '#'
            }
        ],
        
        returnReason: null,
        
        flowHistory: [
            {
                time: '15:20',
                action: '接收',
                content: '[城管局] 已接收工单，正在处理',
                operator: '王五'
            },
            {
                time: '14:45',
                action: '派发',
                content: '[市级中心] 派发至 [城管局]',
                operator: '张三'
            },
            {
                time: '14:30',
                action: '创建',
                content: '[市级中心] 创建工单',
                operator: '张三'
            }
        ]
    },
    
    // 示例工单3 - 社保咨询（已办结）
    'ticket003': {
        woId: '202507281280',
        title: '咨询社保转移手续办理流程',
        content: `我需要将社保从外地转移到本市，请问需要什么材料和手续？

个人情况：
- 原工作地：上海市
- 现工作地：北京市
- 社保缴费年限：8年
- 户籍所在地：河北省

希望了解具体的办理流程、所需材料清单以及办理时限。`,
        
        citizenName: '陈**',
        citizenPhone: '137****9876',
        citizenAddress: 'XX区XX街道XX社区',
        locationCoords: { lat: 39.9242, lng: 116.4274 },
        historicalWoCount: 2,
        historicalSatisfaction: 92,
        
        type: 'consultation',
        businessCategory: '人力社保 → 社会保险 → 转移接续',
        priority: 'normal',
        channel: 'website',
        status: 'closed',
        
        attachments: [],
        
        returnReason: null,
        
        flowHistory: [
            {
                time: '16:45',
                action: '办结',
                content: '[市级中心] 即时办结',
                operator: '张三'
            },
            {
                time: '16:30',
                action: '创建',
                content: '[市级中心] 创建工单',
                operator: '张三'
            }
        ]
    },

    // 示例工单4 - 菜市场环境卫生问题（被退回2次）
    'ticket008': {
        // 基础信息
        woId: '202507289634',
        title: '菜市场环境卫生问题',
        content: `市民反映XX菜市场环境卫生状况较差，地面积水严重，垃圾清理不及时，摊位周围污水横流，异味严重，影响周边居民生活环境和食品安全。

具体问题：
1. 地面积水：菜市场内地面长期积水，特别是蔬菜区和水产区
2. 垃圾处理：垃圾桶满溢，清理不及时，腐烂蔬菜随意丢弃
3. 污水排放：摊位污水直接排放到地面，形成污水沟
4. 异味问题：腐烂蔬菜和污水产生严重异味
5. 食品安全：卫生条件差可能影响食品安全

希望相关部门加强监管，改善菜市场卫生状况，保障市民健康和食品安全。`,

        // 市民信息
        citizenName: '陈**',
        citizenPhone: '137****9876',
        citizenAddress: '东城区XX街道XX菜市场附近',
        locationCoords: { lat: 39.9163, lng: 116.3972 }, // 东城区坐标
        historicalWoCount: 1,
        historicalSatisfaction: 45,

        // 分类信息
        type: 'complaint',
        businessCategory: '市场监管 → 农贸市场 → 环境卫生',
        priority: 'normal',
        channel: 'wechat',
        status: 'draft',

        // 附件信息
        attachments: [
            {
                id: 'att008_001',
                name: '菜市场现状照片1.jpg',
                type: 'image',
                size: '3.1MB',
                url: '#',
                thumbnail: '#'
            },
            {
                id: 'att008_002',
                name: '菜市场现状照片2.jpg',
                type: 'image',
                size: '2.8MB',
                url: '#',
                thumbnail: '#'
            },
            {
                id: 'att008_003',
                name: '污水排放视频.mp4',
                type: 'video',
                size: '18.5MB',
                url: '#',
                thumbnail: '#'
            }
        ],

        // 退回原因（被退回2次）
        return_reason: [
            {
                unit: '区市场监管局',
                reason: '经核实，该菜市场属于XX街道管辖范围，建议转派至街道办事处处理。',
                time: '2025-07-26 16:20'
            },
            {
                unit: 'XX街道办事处',
                reason: '此问题涉及市场内部管理和市政排水系统，需要市场监管局和城管局联合处理，单独街道无法解决。',
                time: '2025-07-27 10:45'
            }
        ],

        // 流转历史（详细的历史追溯）
        flow_history_filtered: [
            {
                time: '09:15',
                action: '创建',
                unit: '市级中心',
                operator: '张三',
                description: '市民通过微信小程序提交工单'
            },
            {
                time: '10:30',
                action: '派发',
                unit: '市级中心',
                operator: '张三',
                description: '派发至区市场监管局处理'
            },
            {
                time: '16:20',
                action: '退回',
                unit: '区市场监管局',
                operator: '李主任',
                description: '退回至市级中心，原因：管辖范围问题'
            },
            {
                time: '08:45',
                action: '重新派发',
                unit: '市级中心',
                operator: '张三',
                description: '重新派发至XX街道办事处'
            },
            {
                time: '10:45',
                action: '退回',
                unit: 'XX街道办事处',
                operator: '王科长',
                description: '退回至市级中心，原因：需要多部门联合处理'
            },
            {
                time: '14:30',
                action: '待处理',
                unit: '市级中心',
                operator: '张三',
                description: '工单退回，等待重新分析和派发'
            }
        ]
    }
};

/**
 * 根据工单ID获取详细数据
 * @param {string} ticketId - 工单ID
 * @returns {Object|null} 详细工单数据
 */
function getDetailedTicketData(ticketId) {
    return DETAILED_TICKET_DATA[ticketId] || null;
}

/**
 * 根据工单状态生成动态操作按钮
 * @param {string} status - 工单状态
 * @param {number} returnCount - 退回次数
 * @returns {Array} 操作按钮配置
 */
function generateActionButtons(status, returnCount = 0) {
    const buttons = [];
    
    switch (status) {
        case 'draft':
            buttons.push(
                { text: '编辑', icon: 'fas fa-edit', type: 'primary', action: 'edit' },
                { text: '指派', icon: 'fas fa-share', type: 'outline', action: 'assign' },
                { text: '即时办结', icon: 'fas fa-check-circle', type: 'outline', action: 'close' }
            );
            break;

        case 'pending':
            buttons.push(
                { text: '催办', icon: 'fas fa-bell', type: 'outline', action: 'urge' },
                { text: '撤回', icon: 'fas fa-undo', type: 'outline', action: 'recall' }
            );
            break;

        case 'processing':
            buttons.push(
                { text: '查看进度', icon: 'fas fa-eye', type: 'outline', action: 'progress' },
                { text: '催办', icon: 'fas fa-bell', type: 'outline', action: 'urge' }
            );
            break;

        case 'closed':
            buttons.push(
                { text: '查看详情', icon: 'fas fa-eye', type: 'outline', action: 'view' }
            );
            break;
    }
    
    // 更多操作菜单
    buttons.push({
        text: '更多',
        icon: 'fas fa-ellipsis-h',
        type: 'outline',
        action: 'more',
        dropdown: [
            { text: '复制工单', action: 'copy' },
            { text: '添加标签', action: 'tag' },
            { text: '添加抄送', action: 'cc' },
            { text: '导出PDF', action: 'export' }
        ]
    });
    
    return buttons;
}

/**
 * 生成工单标签
 * @param {Object} ticketData - 工单数据
 * @returns {Array} 标签配置
 */
function generateTicketTags(ticketData) {
    const tags = [];
    
    // 紧急程度标签
    if (ticketData.priority === 'urgent') {
        tags.push({ text: '紧急', type: 'urgent', icon: 'fas fa-exclamation-triangle' });
    } else if (ticketData.priority === 'high') {
        tags.push({ text: '重要', type: 'important', icon: 'fas fa-exclamation-circle' });
    }
    
    // 退回标签
    if (ticketData.returnCount && ticketData.returnCount > 0) {
        tags.push({ text: '被退回', type: 'returned', icon: 'fas fa-reply' });
    }
    
    // 督办标签（示例）
    if (ticketData.supervised) {
        tags.push({ text: '督办', type: 'supervised', icon: 'fas fa-flag' });
    }
    
    return tags;
}
