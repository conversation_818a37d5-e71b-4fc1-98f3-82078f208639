
---

### **市级工单创建分派人员-工单列表设计**

#### **一、 列表核心展示字段**

为了支撑该角色“受理、创建、即时办结、一级分派”的核心工作，建议列表优先展示以下字段，确保关键信息一目了然。

| 字段名称 | 说明 |
| :--- | :--- |
| **工单编号 (`wo_id`)** | 工单的唯一标识，便于精确查找和沟通。对于【草稿】状态的工单，可显示临时ID。 |
| **诉求标题 (`title`)** | 对市民诉求的快速概览，是判断工单性质的第一入口。 |
| **当前状态 (`current_status`)** | 最核心的状态字段。该角色主要关注**【草稿】**、**【待接收】**（已派发）和**【已关闭】**（即时办结）的工单。 |
| **紧急程度 (`priority`)** | 由创建人员设定的优先级，用于判断处理的紧迫性。 |
| **诉求渠道 (`channel`)** | 标识工单来源，如`电话`、`网站`、`APP`等。 |
| **工单类型 (`wo_type`)** | 标识诉求性质，如`投诉`、`咨询`、`求助`等，是判断能否“即时办结”的重要依据。 |
| **市民姓名 (`citizen_name`)** | 诉求人信息，经过脱敏处理。 |
| **创建人姓名 (`creator_name`)** | 工单的创建人员，在多人协作时便于区分。 |
| **创建时间 (`create_time`)** | 工单的创建时间，用于排序和工作量评估。 |
| **责任单位 (`responsible_unit`)** | **关键分派信息**。对于【待接收】工单，显示已派发的单位；对于【草稿】工单，此栏为空。 |
| **总体处理剩余时间 (`remaining_time_total`)** | 监控已派发工单的SLA时效，对即将超时的工单可进行催办。 |

#### **二、 列表页操作功能**

根据角色权限矩阵和业务流程，市级工单创建分派人员在列表页应具备以下操作权限：

##### **1. 页面级操作 (位于列表顶部)**

这些是针对整个列表或批量选择后执行的操作。

*   **`新建工单`**: 从无到有创建一张全新的工单，是该角色的核心功能。
*   **`刷新列表`**: 手动更新工单列表，获取最新数据。
*   **`搜索工单`**: 提供多条件组合搜索功能，快速定位目标工单。
*   **`筛选工单`**: 按状态、时间、类型等预设条件过滤工单列表。
*   **`批量即时办结`**: 【高频操作】对多个无需流转的咨询、告知类工单进行一次性批量关闭。
*   **`批量指派工单`**: 对多个性质相同的【草稿】状态工单，批量派发给同一个承办单位。
*   **`导出工单`**: 将当前列表或选中工单的数据导出为Excel文件。
*   **更多操作菜单**:
    *   **`工单合并`**: 将多个反映同一问题的重复工单合并为一个，上报主管审核。
    *   **`工单拆分`**: 将一个包含多项不同诉求的工单拆分为多个，上报主管审核。

##### **2. 行内操作 (位于每条工单右侧)**

这些是针对单条工单执行的精细化操作。

*   **`查看工单详情`**: 点击进入工单的完整信息页面。
*   **`编辑工单`**: 【核心功能】仅对**【草稿】**状态或被退回的工单可用，用于修改和完善工单信息。
*   **`即时办结`**: 【高频操作】对无需下派的咨询、告知类工单，直接办结关闭。
*   **`指派工单`**: 【核心功能】对**【草稿】**状态的工单，将其派发给指定的承办单位。
*   **`催办工单`**: 对已派发但对方长时间未接收的**【待接收】**工单，发送处理提醒。
*   **`撤回工单`**: 在派发后且对方尚未接收处理的情况下，可撤回错误的指派。
*   **更多操作菜单**:
    *   **`复制工单`**: 基于现有工单信息快速创建一张相似的新工单，提高录入效率。
    *   **`添加标签`**: 为工单添加如“#重复来电#”等标签，便于分类和搜索。
    *   **`添加抄送`**: 将工单抄送给相关领导或部门知晓。

---

### **市级工单创建分派人员的筛选与查询设计**

#### **一、 快捷筛选 (预设视图)**

快捷筛选通常以“页签”或“按钮组”的形式固定在列表顶部，提供一键切换到最常用的工作视图。这能满足角色80%以上的日常筛选需求。

| 筛选名称 | 筛选逻辑 (筛选条件) | 主要用途 |
| :--- | :--- | :--- |
| **我的草稿** | `当前状态` = 【草稿】 AND `创建人` = 当前用户 | **核心工作区**。显示所有待编辑、待完善、待派发的工单，是该角色的主要待办列表。 |
| **被退回件** | `当前状态` = 【草稿】 AND `退回次数` > 0 | **优先处理区**。快速筛选出被下级单位退回的工单，需要立即重新研判和分派。 |
| **已派待接** | `当前状态` = 【待接收】 | **监控区**。查看已经派发出去，但承办单位尚未点击“接收”的工单，便于及时跟进或催办。 |
| **我已办结** | `关闭类型` = 【即时办结关闭】 AND `创建人` = 当前用户 | **复核区**。查看自己通过“即时办结”直接关闭的工单，用于工作复盘或应对市民再次来电。 |
| **今日新增** | `创建时间` = 今天 | **日清日结**。快速查看当天创建的所有工单，便于评估当日工作量。 |
| **紧急/重大** | `紧急程度` IN (`紧急`, `重大`) | **重点关注**。高优筛选出需要优先处理和派发的紧急、重大工单。 |
| **全部工单** | 无 | 查看权限范围内的所有工单。 |

**设计建议**：
*   默认进入列表时，自动应用 **“我的草稿”** 筛选，让用户直接看到待办任务。
*   在“被退回件”、“已派待接”等标签上，可增加数字角标，实时显示该分类下的工单数量，提供更直观的提醒。

#### **二、 查询功能 (多条件搜索)**

查询功能是为了满足更复杂、更精细的查找需求，通常分为“基础搜索框”和“高级搜索”两种形式。

##### **1. 基础搜索框 (常驻显示)**

这是一个位于列表上方的、最简单直接的输入框。为了提升效率，它应该支持对多个核心字段的模糊匹配。

**建议搜索范围**:
*   **工单编号 (`wo_id`)**
*   **诉求标题 (`title`)**
*   **市民姓名 (`citizen_name`)**
*   **联系电话 (`citizen_phone`)**

用户输入关键词后，系统会同时在以上四个字段中进行查找，快速定位。

##### **2. 高级搜索 (可展开)**

点击“高级搜索”或“更多筛选条件”按钮后，会展开一个包含更多字段的表单，允许用户进行组合查询。

| 查询类别 | 包含字段 | 说明 |
| :--- | :--- | :--- |
| **基础信息** | `工单编号`、`诉求标题`、`诉求内容` | 提供精确或模糊查询。 |
| **市民信息** | `市民姓名`、`联系电话`、`客户类型` | 用于通过来电人信息反查工单。 |
| **分类与来源** | `诉求渠道` (枚举)、`工单类型` (枚举)、`业务分类` (树形结构)、`紧急程度` (枚举) | 用于按工单属性进行归类分析或查找。 |
| **时间范围** | `创建时间` (日期范围)、`提交时间` (日期范围) | 支持按“天”、“周”、“月”或自定义时间段进行查询。 |
| **责任与状态** | `创建人姓名`、`责任单位` (已派发的单位)、`当前状态` | 用于查找特定同事创建的工单、特定单位承办的工单或特定状态的工单。 |
| **事发位置** | `事发行政区划` (树形结构)、`详细地址` | 用于根据地理位置查找相关工单。 |

**查询功能操作按钮**:
*   **`查询`**: 执行搜索。
*   **`重置`**: 清空所有已填写的查询条件。
*   **`收起`**: 关闭高级搜索表单。
*   **(可选) `保存为常用查询`**: 允许用户将常用的复杂查询条件组合保存下来，方便日后一键调用。

---

### **“快速预览”功能设计**

#### **一、 核心理念与目标**

*   **目标**：在不跳转页面的前提下，为用户提供一个“信息摘要 + 快捷操作”的窗口。
*   **用户场景**：当市级工单创建分派人员在列表中看到一条工单时，希望快速判断其性质（能否即时办结？应派给谁？），并立即采取行动。
*   **设计原则**：信息聚焦、主次分明、操作前置。

#### **二、 交互方式：侧边抽屉式面板 (Side Panel/Drawer)**

当用户点击列表中的某条工单标题或一个专门的“预览”图标时，从页面右侧平滑地滑出一个信息面板。

*   **优点**：
    1.  **不中断浏览**：用户视线无需离开列表，左侧的工单列表依然可见，便于在多个工单之间快速切换预览。
    2.  **内容承载力强**：相比于在行内展开，侧边面板可以容纳更丰富的信息和更复杂的布局。
    3.  **交互体验好**：动画效果流畅，关闭和打开都非常快捷。

#### **三、 快速预览面板内容结构**

面板内容将根据该角色的核心需求，进行精心编排，分为四大区域：**顶部标题栏、核心操作区、关键信息区、历史追溯区**。

---

**【快速预览面板 - 示例布局】**

| **[顶部标题栏]** |
| :--- |
| **工单编号: `SZ20250728-00001`** |
| **诉求标题: 关于XX小区夜间施工噪音扰民的投诉** |
| [标签: `紧急` `被退回`] [关闭按钮 X] |

| **[核心操作区]** (根据工单状态动态显示) |
| :--- |
| `编辑` `指派` `即时办结` `更多(...)` |

| **[关键信息区]** (带滚动条) |
| :--- |
| **一、 核心诉求详情** |
| **诉求内容 (`content`):** |
| > 市民反映，自本周一开始，位于XX区XX路的XX小区旁的建筑工地，每天晚上11点后依然进行土方作业，噪音巨大，严重影响了周边居民的正常休息，希望相关部门核实处理... (显示全部内容) |
| **附件列表 (`attachments`):** |
| > [缩略图] 现场照片.jpg [缩略图] 施工视频.mp4 (点击可放大预览) |
| |
| **二、 市民与位置信息** |
| **市民姓名:** 王** (`citizen_name`) | **联系电话:** 138****1234 (`citizen_phone`) [复制] |
| **历史工单:** **3** 次 (`historical_wo_count`) | **历史满意度:** **67%** (`historical_satisfaction`) [查看历史] |
| **详细地址 (`location_address`):** XX区XX路XX小区3栋 |
| **地图定位 (`location_coords`):** |
| > [此处内嵌一个小的、可缩放的静态地图，清晰标出事发点] |
| |
| **三、 工单分类属性** |
| **工单类型:** `投诉` | **业务分类:** `城市管理 -> 工地管理 -> 工地噪音` |
| **紧急程度:** `紧急` | **诉求渠道:** `电话` |
| |

| **[历史追溯区]** |
| :--- |
| **退回原因 (`return_reason`):** (仅当被退回时以醒目方式显示) |
| > **[区城管局] 退回:** 此事发地经核实属于XX街道管辖范围，非我局直管，请改派。 |
| **流转历史 (`flow_history_filtered`):** |
| > 10:35 - [市级中心] **派发**至 [区城管局] |
| > 09:30 - [市级中心] **创建**工单 |

---

#### **四、 面板各区域详解**

1.  **顶部标题栏**:
    *   **工单编号/标题**: 最核心的标识，让用户确认正在查看的是哪一张单。
    *   **标签**: 用醒目的颜色标签展示工单的特殊状态，如`紧急`、`重大`、`被退回`、`督办`，实现“秒级”判断。
    *   **关闭按钮**: 允许用户快速关闭预览面板。

2.  **核心操作区**:
    *   **动态按钮**: 此区域的按钮会根据工单的`current_status`动态变化。
        *   **对于【草稿】工单**: 显示 `编辑`、`指派`、`即时办结`。这是最高频的操作。
        *   **对于【待接收】工单**: 显示 `催办`、`撤回`。
    *   **“更多”菜单**: 收纳 `复制工单`、`添加标签`、`添加抄送` 等次要操作，保持界面整洁。

3.  **关键信息区**:
    *   **核心诉求详情**: 将市民的**完整诉求内容**和**附件**放在最前面，这是判断问题性质的基础。
    *   **市民与位置信息**:
        *   提供市民的**历史投诉次数**和**满意度**，是判断该市民是否为“重点关注对象”的关键。点击可跳转至该市民的完整历史工单列表。
        *   **内嵌地图**比纯文字地址更直观，极大地帮助分派人员判断正确的属地责任单位（例如，判断一个地址究竟属于A街道还是B街道）。
    *   **工单分类属性**: 快速了解工单的标准化分类，辅助决策。

4.  **历史追溯区**:
    *   **退回原因**: 如果是退回件，**必须将退回原因放在最醒目的位置**。这是重新派单最重要的依据，避免二次派错。
    *   **流转历史**: 简明地展示工单的生命周期，帮助理解工单当前所处的阶段。